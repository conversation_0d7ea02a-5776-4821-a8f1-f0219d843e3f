/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      'placehold.co',
      'd1b75btxwpe6g0.cloudfront.net',
      'ddu7t1mnsh657.cloudfront.net',
      'lh3.googleusercontent.com',
    ],
  },
  reactStrictMode: false,
  experimental: {
    serverActions: {
      allowedOrigins: [
        'nvproject-git-add-lease-rewrite-nantucket-rentals.vercel.app',
        'devpm.congdonandcoleman.com',
        'cloud.congdonandcoleman.com',
      ],
    },
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ];
  },
  webpack: (config) => {
    /**
     * Critical: prevents " ⨯ ./node_modules/canvas/build/Release/canvas.node
     * Module parse failed: Unexpected character '�' (1:0)" error
     */
    config.resolve.alias.canvas = false;

    return config;
  },
};

export default nextConfig;
