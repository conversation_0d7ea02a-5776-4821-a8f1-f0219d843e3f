'use server';
import {
  DisbursementInfo,
  LeasePayload,
  SecurityDepositReturnInfo,
} from '@/types/lease';
import { cookies } from 'next/headers';
const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getAuthorizedHeader = (json?: boolean) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const contentType = json ? { 'Content-Type': 'application/json' } : {};

  return {
    Authorization: `JWT ${token}`,
    ...contentType,
  };
};

export const getLeaseComments = async <T>(id: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const res = await fetch(
      `${BASE_URL}/security-deposit/${id}/comments?offset=0&limit=100`,
      {
        headers: {
          Authorization: `JWT ${token}`,
        },
        next: {
          revalidate: 0,
          tags: [`lease-comments-${id}`],
        },
      }
    );

    if (res.ok) {
      return res.json() as T;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch lease comments');
  }
};

export const addLeaseComment = async (
  id: number,
  data: {
    content: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  try {
    const res = await fetch(`${BASE_URL}/security-deposit/${id}/comments`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (res.ok) {
      return res.json();
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to add lease comment');
  }
};

export const deleteLeaseComment = async (id: number, commentId: string) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  try {
    const res = await fetch(
      `${BASE_URL}/security-deposit/${id}/comments/${commentId}`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `JWT ${token}`,
        },
      }
    );

    if (res.ok) {
      return;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to delete lease comment');
  }
};

export const getLeaseDetails = async <T>(id: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  try {
    const res = await fetch(`${BASE_URL}/leases/${id}`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 0, tags: [`lease-details-${id}`] },
    });
    if (res.ok) {
      return res.json() as T;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch lease details');
  }
};

export const sendSignRequest = async <T>(data: {
  lease: number;
  to: 'tenant' | 'homeowner';
}) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const res = await fetch(`${BASE_URL}/send-sign`, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(data),
    });

    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to send sign request');
  }
};

export const sendLeaseComment = async (leaseId: number, comment: any) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const res = await fetch(
      `${BASE_URL}/security-deposit/${leaseId}/comments`,
      {
        headers: {
          Authorization: `JWT ${token}`,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        method: 'POST',
        body: comment,
      }
    );

    if (res.ok) {
      return res.json();
    }
  } catch {
    console.log('something happened');
  }
};

export const uploadSignedLease = async (data: FormData) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  try {
    const res = await fetch(`${BASE_URL}/upload-signed-file`, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: data,
    });

    if (res.ok) {
      return res.json();
    }
  } catch {
    console.log('something happened');
  }
};

export const recordTransaction = async <T>(
  paymentId: string,
  data: {
    payment: string;
    amount: string;
    payment_date: string;
    reference: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/payment/${paymentId}/record-transaction`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(data),
    });

    const responseData = await res.json();
    return { data: responseData as T, status: res.status };
  } catch (error) {
    console.error('Error recording transaction:', error);
    throw new Error('Failed to record transaction');
  }
};

export const recordAdjustment = async <T>(paymentId: string, data: any) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/payment/${paymentId}/record-adjustment`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(data),
    });

    console.log({
      status: res.status,
      statusText: res.statusText,
    });

    const responseData = await res.json();
    return { data: responseData as T, status: res.status };
  } catch (error) {
    console.error('Error recording adjustment:', error);
    throw new Error('Failed to record adjustment');
  }
};

export const createLease = async <T>(data: LeasePayload) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/leases`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(data),
    });

    const responseData = await res.json();

    if (!res.ok) {
      console.log({ res, responseData });
      throw new Error('Failed to create Lease');
    }

    return responseData as T;
  } catch {
    console.log('something happened');
  }
};

export const updateLease = async <T>(id: number, data: LeasePayload) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/leases/${id}`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'PATCH',
      body: JSON.stringify(data),
    });

    const responseData = await res.json();

    if (!res.ok) {
      console.log({ res, responseData });
      throw new Error('Failed to update Lease');
    }

    return responseData as T;
  } catch {
    console.log('something happened');
  }
};

export const sendReminderAgent = async (data: { leaseId: number }) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/notification-agent-payment-due`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (!res.ok) {
      console.log('res', res);
      throw new Error('Failed to send reminder');
    }

    return res.json();
  } catch {
    console.log('something happened');
  }
};

export const submitDisbursement = async (
  paymentId: string,
  data: DisbursementInfo
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/disbursement-form/${paymentId}`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'PATCH',
      body: JSON.stringify(data),
    });

    const responseData = await res.json();
    console.log({ res, responseData });
    return { data: responseData, status: res.status };
  } catch (error) {
    console.error('Error submitting disbursement:', error);
    throw new Error('Failed to submit disbursement');
  }
};

export const returnSecurityDeposit = async (
  leaseId: number,
  data: { return_info: SecurityDepositReturnInfo }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const url = `${BASE_URL}/security-deposit/${leaseId}/return`;

  try {
    const res = await fetch(url, {
      headers: {
        Authorization: `JWT ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify(data),
    });

    const responseData = await res.json();
    console.log({ res, responseData });
    return { data: responseData, status: res.status };
  } catch (error) {
    console.error('Error returning security deposit:', error);
    throw new Error('Failed to return security deposit');
  }
};

export const uploadSecurityDepositFiles = async (
  leaseId: number,
  data: {
    attach_file: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(
    `${BASE_URL}/security-deposit/${leaseId}/upload-file`,
    {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `JWT ${token}`,
      },
      body: JSON.stringify(data),
    }
  );

  if (!res.ok) {
    throw new Error('Failed to upload file');
  }

  return res.json();
};

export const removeSecurityDepositFiles = async (
  leaseId: number,
  data: {
    attach_file: string;
  }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(
    `${BASE_URL}/security-deposit/${leaseId}/remove-file`,
    {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `JWT ${token}`,
      },
      body: JSON.stringify(data),
    }
  );

  if (!res.ok) {
    throw new Error('Failed to remove file');
  }

  return res.json();
};

export const uploadFile = async (payload: FormData) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  try {
    const res = await fetch(`${BASE_URL}/upload/file`, {
      method: 'POST',
      headers: {
        Authorization: `JWT ${token}`,
      },
      body: payload,
    });

    const responseData = await res.json();
    console.log({ res, responseData });
    return { data: responseData, status: res.status };
  } catch (error) {
    console.error('Error uploading file:', error);
    throw new Error('Failed to upload file');
  }
};
