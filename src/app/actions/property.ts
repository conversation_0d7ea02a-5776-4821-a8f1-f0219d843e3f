'use server';

import { EmailContactPayload, ShareListingPayload } from '@/types/property';
import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getPropertyDetails = async <T>(id: number, query?: string) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  const querySt = query ? query : '?show_price=true';
  try {
    const res = await fetch(`${BASE_URL}/listings/${id}${querySt}`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 0, tags: [`property-details-${id}`] },
    });

    if (res.ok) {
      return res.json() as T;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch property details');
  }
};

export const updatePropertyDetails = async <T>(id: number, payload: any) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  try {
    const res = await fetch(`${BASE_URL}/listings/${id}`, {
      method: 'PATCH',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `JWT ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!res.ok) {
      throw new Error('Failed to update property details');
    }

    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to update property details');
  }
};

export const addPropertyComment = async (
  id: number,
  payload: { content: string }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/listings/${id}/comments`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    throw new Error('Failed to add notes');
  }

  return res.json();
};

export const getPropertyNotes = async <T>(id: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(
    `${BASE_URL}/listings/${id}/comments?offset=0&limit=3`,
    {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: [`property-comments-${id}`] },
    }
  );

  if (!res.ok) {
    throw new Error('Failed to add notes');
  }

  return res.json() as T;
};

export const getAreas = async <T>() => {
  try {
    const res = await fetch(`${BASE_URL}/area`, {
      next: { revalidate: 10, tags: [`area`] },
    });

    if (res.ok) {
      return res.json() as T;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch areas');
  }
};

export const uploadPhoto = async (payload: FormData) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/upload/image`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `JWT ${token}`,
    },
    body: payload,
  });

  if (!res.ok) {
    throw new Error('Failed to upload photo');
  }

  return res.json();
};

export const getListingAddresses = async <T>(query: string = '') => {
  const res = await fetch(`${BASE_URL}/listings?limit=10&address=${query}`);

  if (!res.ok) {
    throw new Error('Failed to fetch listing addresses');
  }

  return res.json() as T;
};

export const uploadFile = async <T>(formData: FormData) => {
  console.log('form data', formData);
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/external-tool`, {
    method: 'POST',
    headers: {
      Authorization: `JWT ${token}`,
    },
    body: formData,
  });

  if (!res.ok) {
    console.log('Error', res, res.headers);
    throw new Error('Failed to upload file');
  }

  return res.json() as T;
};

export const uploadImage = async <T>(formData: FormData) => {
  console.log('form data', formData);
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/upload/image`, {
    method: 'POST',
    headers: {
      Authorization: `JWT ${token}`,
    },
    body: formData,
  });

  if (!res.ok) {
    console.log('Error', res, res.headers);
    throw new Error('Failed to upload file');
  }

  return res.json() as T;
};

export const getPropertySeasonalAvailability = async <T>(
  id: number,
  startDate: string
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  try {
    const res = await fetch(
      `${BASE_URL}/seasonal-availability?listing_id=${id}&startDate=${startDate}`,
      {
        headers: {
          Authorization: `JWT ${token}`,
        },
        next: { revalidate: 0, tags: [`property-seasonal-availability-${id}`] },
      }
    );

    if (res.ok) {
      return res.json() as T;
    }
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch property seasonal availability');
  }
};

export const publishListing = async <T>(id: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/listings/${id}/publish`, {
    method: 'POST',
    headers: {
      Authorization: `JWT ${token}`,
    },
  });

  if (!res.ok) {
    console.log('Error', res);
    throw new Error('Failed to publish listing');
  }

  return res.json() as T;
};

export const hideListing = async <T>(id: number) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/listings/${id}/hide`, {
    method: 'POST',
    headers: {
      Authorization: `JWT ${token}`,
    },
  });

  if (!res.ok) {
    console.log('Error', res);
    throw new Error('Failed to hide listing');
  }

  return res.json() as T;
};

export const deactivateListing = async <T>(
  id: number,
  payload: { deactivated_reason: string }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/listings/${id}/deactivate`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log('Error', res);
    throw new Error('Failed to deactivate listing');
  }

  return res.json() as T;
};

export const shareListing = async <T>(payload: ShareListingPayload) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/share-listings`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log('Error', res);
    throw new Error('Failed to share listing');
  }

  return res.json() as T;
};

export const createContactComment = async <T>(
  contactId: number,
  payload: { content: string }
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const res = await fetch(`${BASE_URL}/contacts/${contactId}/comments`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `JWT ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log('Error', res);
    throw new Error('Failed to share listing to contact');
  }

  return res.json() as T;
};

export const emailListingHomeowner = async <T>(
  payload: EmailContactPayload | FormData
) => {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;
  const headers: HeadersInit =
    payload instanceof FormData
      ? {
          Authorization: `JWT ${token}`,
        }
      : {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `JWT ${token}`,
        };
  const res = await fetch(`${BASE_URL}/email-contact`, {
    method: 'POST',
    headers,
    body: payload instanceof FormData ? payload : JSON.stringify(payload),
  });

  if (!res.ok) {
    console.log('Error', res);
    throw new Error('Failed to send email to contact');
  }

  return res.json() as T;
};

export const calculateRentForListing = async <T>(payload: {
  arrival_date: string;
  departure_date: string;
  listing: number;
}) => {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('token')?.value;
    const res = await fetch(`${BASE_URL}/calculate-rent`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `JWT ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || 'Failed to calculate rent');
    }

    return (await res.json()) as T;
  } catch (error) {
    return Promise.reject(error);
  }
};
