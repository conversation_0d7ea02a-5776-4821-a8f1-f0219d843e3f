import { Lease } from '@/types/lease';
import dayjs from 'dayjs';

type Props = {
  lease?: Lease;
};
const LeaseDetailsSection = ({ lease }: Props) => {
  return (
    <div className="p-4 bg-navy rounded-[10px] grid md:grid-cols-2 xl:grid-cols-3 gap-2">
      <div className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between bg-white">
        <p className="text-[10px] md:text-xs flex-grow">
          Address:
          <span className="font-bold ml-4">{lease?.listing_address ?? ''}</span>
        </p>
      </div>
      <div className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between bg-white">
        <p className="text-[10px] md:text-xs flex-grow">
          Tenant:
          <span className="font-bold ml-4">
            {lease?.tenant_first_name + ' ' + lease?.tenant_last_name}
          </span>
        </p>
      </div>
      <div className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between bg-white">
        <p className="text-[10px] md:text-xs flex-grow">
          Leasing Agent:
          <span className="font-bold ml-4">
            {lease?.user_first_name + ' ' + lease?.user_last_name}
          </span>
        </p>
      </div>
      <div className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between bg-white">
        <p className="text-[10px] md:text-xs flex-grow">
          Landlord:
          <span className="font-bold ml-4">
            {lease?.owner_first_name + ' ' + lease?.owner_last_name}
          </span>
        </p>
      </div>
      <div className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between bg-white">
        <p className="text-[10px] md:text-xs flex-grow">
          Arr. / Dep.:
          <span className="font-bold ml-4">
            {lease?.arrival_date + ' - ' + lease?.departure_date}
          </span>
        </p>
      </div>
      <div className="border rounded-lg px-2 md:px-4 py-2 flex items-center justify-between bg-white">
        <p className="text-[10px] md:text-xs flex-grow">
          Lease Created:
          <span className="font-bold ml-4">
            {lease ? dayjs(lease.created_at).format('MM/DD/YYYY') : 'N/A'}
          </span>
        </p>
      </div>
    </div>
  );
};

export default LeaseDetailsSection;
