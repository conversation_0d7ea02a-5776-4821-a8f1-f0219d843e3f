import { Nullable } from '@/types/common';
import { SecurityDeposit } from '@/types/lease';
import { currencyFormatter, parseDateString } from '@/utils/common';
import {
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import SecurityDepositReturnForm from '@/clients/views/lease/financial-summary/SecurityDepositReturnForm';
import { format } from 'date-fns';

type Props = {
  securityDeposit: Nullable<SecurityDeposit>;
  leaseId: number;
};
const LeaseSecurityDeposit = ({ securityDeposit, leaseId }: Props) => {
  return (
    <div className="p-4 bg-white rounded-md">
      <p className="m-0 text-sm font-semibold flex items-center">
        {securityDeposit?.status === 'Paid' ? (
          <CheckCircleIcon className="w-5 h-5 stroke-2 text-green-500 mr-4" />
        ) : (
          <InformationCircleIcon className="w-5 h-5 stroke-2 text-red-500 mr-4" />
        )}
        Security Deposit
      </p>
      <table className="w-full text-xs overflow-x-auto">
        <thead className="text-black/60">
          <tr className="border-b border-outline">
            <th className="py-4 text-left font-normal w-[20%]">Type</th>
            <th className="py-4 text-center font-normal w-[18%]">Date</th>
            <th className="py-4 text-center font-normal w-[26%]">Reference</th>
            <th className="py-4 font-normal text-center w-[18%]">Amount</th>
            <th className="py-4 font-normal w-[18%] text-right">Balance</th>
          </tr>
        </thead>
        <tbody>
          <tr className="border-b border-outline">
            <td className="py-4 text-left font-normal w-[20%]">
              Received from Tenant
            </td>
            <td className="py-4 text-center font-normal w-[18%]"></td>
            <td className="py-4 text-center font-normal w-[26%]"></td>
            <td className="py-4 font-normal text-center w-[18%]">
              {currencyFormatter.format(
                Number(securityDeposit?.amount_received ?? 0)
              )}
            </td>
            <td className="py-4 font-normal w-[18%] text-right">
              {currencyFormatter.format(Number(securityDeposit?.balance ?? 0))}
            </td>
          </tr>
          <tr className="border-b border-outline">
            <td className="py-4 text-left font-normal w-[20%]">
              Payout to Owner
            </td>
            <td className="py-4 text-center font-normal w-[18%]"></td>
            <td className="py-4 text-center font-normal w-[26%]">
              {securityDeposit?.return_info?.sd_homeowner_bill_doc_number}
            </td>
            <td className="py-4 font-normal text-center w-[18%]">
              {currencyFormatter.format(
                Number(securityDeposit?.return_info?.homeowner ?? 0)
              )}
            </td>
            <td className="py-4 font-normal w-[18%] text-right">
              {currencyFormatter.format(Number(securityDeposit?.balance ?? 0))}
            </td>
          </tr>
          <tr>
            <td className="py-4 text-left font-normal w-[20%]">
              Return to Tenant
            </td>
            <td className="py-4 text-center font-normal w-[18%]">
              {format(
                parseDateString(securityDeposit?.return_deadline ?? ''),
                'LLL d, yyyy'
              )}
            </td>
            <td className="py-4 text-center font-normal w-[26%]">
              {securityDeposit?.return_info?.sd_tenant_bill_doc_number}
            </td>
            <td className="py-4 font-normal text-center w-[18%]">
              {currencyFormatter.format(
                Number(securityDeposit?.return_info?.tenant ?? 0)
              )}
            </td>
            <td className="py-4 font-normal w-[18%] text-right">
              {currencyFormatter.format(Number(securityDeposit?.balance ?? 0))}
            </td>
          </tr>
        </tbody>
      </table>
      <div className="flex items-center justify-between my-2 text-xs">
        Total <span>{securityDeposit?.amount}</span>
      </div>
      <div className="flex items-center justify-between my-2 text-xs">
        Total Received <span>{securityDeposit?.amount_received}</span>
      </div>
      <div className="flex items-center justify-between text-xs font-semibold">
        Balance <span>{securityDeposit?.balance}</span>
      </div>
      <SecurityDepositReturnForm
        leaseId={leaseId}
        securityDeposit={securityDeposit}
        disabled={securityDeposit?.status === 'Paid'}
      />
    </div>
  );
};

export default LeaseSecurityDeposit;
