import LeasePaymentSendRemainderButton from '@/clients/views/lease/financial-summary/LeasePaymentSendRemainderButton';
import RecordAdjustmentForm from '@/clients/views/lease/financial-summary/RecordAdjustmentForm';
import RecordPaymentForm from '@/clients/views/lease/financial-summary/RecordPaymentForm';
import SubmitDisbursementButton from '@/clients/views/lease/financial-summary/SubmitDisbursementButton';
import { Payments } from '@/types/lease';
import { currencyFormatter } from '@/utils/common';
import {
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import dayjs from 'dayjs';

type Props = {
  payment: Payments;
  isLast?: boolean;
};
const LeasePaymentItem = ({ payment, isLast }: Props) => {
  return (
    <div className="p-2 rounded-md bg-[#F6F6F6] h-min">
      <div className="bg-[#D6EEDD] p-4 rounded-md mb-2">
        <div className="flex items-center justify-between">
          <p className="m-0 text-sm font-semibold flex items-center">
            {payment.status === 'Paid' ? (
              <CheckCircleIcon className="w-5 h-5 stroke-2 text-green-500 mr-4" />
            ) : (
              <InformationCircleIcon className="w-5 h-5 stroke-2 text-red-500 mr-4" />
            )}
            Payment {payment.period}
          </p>
          {payment.status !== 'Paid' && (
            <LeasePaymentSendRemainderButton leaseId={payment.lease} />
          )}
        </div>
        <table className="w-full text-xs overflow-x-auto">
          <thead className="">
            <tr className="border-b border-black">
              <th className="py-4 text-left font-normal w-[22%]">Date</th>
              <th className="py-4 text-left font-normal w-[34%]">Reference</th>
              <th className="py-4 text-left font-normal w-[22%]">Amount</th>
              <th className="py-4xw font-normal w-[22%] text-right">Balance</th>
            </tr>
          </thead>
          <tbody>
            <tr className="">
              <td className="py-2 w-[22%]"></td>
              <td className="py-2 w-[34%]">From Lease</td>
              <td className="py-2 w-[22%]"></td>
              <td className="py-2 w-[22%] text-right">
                ({currencyFormatter.format(Number(payment.balance ?? 0))})
              </td>
            </tr>
            {payment?.transactions?.map((transaction, index) => (
              <tr key={index} className="">
                <td className="py-2 w-[22%]">
                  {dayjs(transaction.payment_date).format('MM/DD/YYYY')}
                </td>
                <td className="py-2 w-[34%]">
                  {transaction?.reference ?? payment?.import_id ?? 'N/A'}
                </td>
                <td className="py-2 w-[22%]">
                  ({currencyFormatter.format(Number(transaction.amount ?? 0))})
                </td>
                <td className="py-2 w-[22%] text-right">
                  (
                  {currencyFormatter.format(
                    Number(transaction.balance_after_transaction ?? 0)
                  )}
                  )
                </td>
              </tr>
            ))}
            {payment?.incoming_adjustments?.map((_a, index) => (
              <tr key={index} className="">
                <td className="py-2 w-[22%]">
                  {dayjs(_a.payment_date).format('MM/DD/YYYY')}
                </td>
                <td className="py-2 w-[34%]">
                  {_a?.reference ?? payment?.import_id ?? 'N/A'}
                </td>
                <td className="py-2 w-[22%]">
                  ({currencyFormatter.format(Number(_a.amount ?? 0))})
                </td>
                <td className="py-2 w-[22%] text-right">
                  ({currencyFormatter.format(Number(payment.balance ?? 0))})
                </td>
              </tr>
            ))}
            <tr className="font-bold border-t border-black">
              <td className="w-auto py-2">Total Received</td>
              <td />
              <td className="w-[22%] py-2">
                {currencyFormatter.format(Number(payment.amount_received ?? 0))}
              </td>
              <td className="w-[22%] text-right py-2">
                {currencyFormatter.format(Number(payment.balance ?? 0))}
              </td>
            </tr>
            {payment?.outgoing_adjustments?.map((_a, index) => (
              <tr key={index} className="text-error">
                <td className="py-2 w-[22%]">
                  {dayjs(_a.payment_date).format('MM/DD/YYYY')}
                </td>
                <td className="py-2 w-[34%]">
                  {_a?.reference ?? payment?.import_id ?? 'N/A'}
                </td>
                <td className="py-2 w-[22%]">
                  ({currencyFormatter.format(Number(_a.amount ?? 0))})
                </td>
                <td className="py-2 w-[22%] text-right">0</td>
              </tr>
            ))}
          </tbody>
        </table>
        <RecordPaymentForm
          payment_uuid={payment.payment_uuid}
          isCompleted={payment.status === 'Paid'}
          leaseId={payment.lease}
          balance={Number(payment.balance)}
        />
        <RecordAdjustmentForm
          payment_uuid={payment.payment_uuid}
          isCompleted={payment.status === 'Paid' || isLast}
          leaseId={payment.lease}
          totalAmount={Number(payment.total_amount)}
        />
      </div>
      <div className="p-4 bg-white rounded-md">
        <div className="flex items-center justify-between">
          <p className="m-0 text-sm font-semibold flex items-center">
            {payment.disbursement_form.submit_at ? (
              <CheckCircleIcon className="w-5 h-5 stroke-2 text-green-500 mr-4" />
            ) : (
              <InformationCircleIcon className="w-5 h-5 stroke-2 text-red-500 mr-4" />
            )}
            Disbursement {payment.period}
          </p>
        </div>
        <table className="w-full text-xs overflow-x-auto">
          <thead className="text-black/60">
            <tr className="border-b border-outline">
              <th className="py-2 text-left font-normal w-[30%]">
                Description
              </th>
              <th className="py-2 text-left font-normal w-[20%]">Date</th>
              <th className="py-2 text-left font-normal w-[30%]">Reference</th>
              <th className="py-2 font-normal w-[20%] text-right">Total</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">
                Occupancy Tax
              </td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.tax ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">
                Processing Fee
              </td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.processing_fee ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">
                Rent to Owner
              </td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.rent_to_owner ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">
                Fees to Owner
              </td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.rent_to_owner ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">Agent Comm</td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.agent_commission ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">
                CoBroke Comm
              </td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.co_broke_commission ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">C&C Comm</td>
              <td className="py-2 text-left font-normal w-[20%]">
                {payment.due_date}
              </td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.office_commission ?? 0)
                )}
              </td>
            </tr>
            <tr className="border-b border-outline">
              <td className="py-2 text-left font-normal w-[30%]">
                To Sec. Deposit
              </td>
              <td className="py-2 text-left font-normal w-[20%]"></td>
              <td className="py-2 text-left font-normal w-[30%]"></td>
              <td className="py-2 font-normal w-[20%] text-right">
                {currencyFormatter.format(
                  Number(payment?.disbursement_form?.security_deposit ?? 0)
                )}
              </td>
            </tr>
          </tbody>
        </table>
        <div className="flex items-center justify-between my-2 text-xs">
          Total Payments <span>{payment.total_amount}</span>
        </div>
        <div className="flex items-center justify-between my-2 text-xs">
          Total Received <span>{payment.amount_received}</span>
        </div>
        <div className="flex items-center justify-between my-2 text-xs font-semibold">
          Balance <span>{payment.balance}</span>
        </div>
        <SubmitDisbursementButton
          disabled={
            payment.status !== 'Paid' || !!payment?.disbursement_form?.submit_at
          }
          disbursementForm={payment?.disbursement_form}
          leaseId={payment.lease}
        />
      </div>
    </div>
  );
};

export default LeasePaymentItem;
