import SecurityDepositDeleteAttachedFileButton from '@/clients/views/lease/financial-summary/SecurityDepositDeleteAttachedFileButton';
import SecurityDepositUploadDocument from '@/clients/views/lease/financial-summary/SecurityDepositUploadDocument';
import { Nullable } from '@/types/common';
import { SecurityDeposit } from '@/types/lease';
import { DocumentIcon } from '@heroicons/react/24/outline';

type Props = {
  securityDeposit: Nullable<SecurityDeposit>;
  leaseId: number;
};

const LeaseAttachedFiles = ({ securityDeposit, leaseId }: Props) => {
  return (
    <div className="p-4 bg-white rounded-md mt-2">
      <p className="my-2 text-sm font-semibold flex items-center">
        Attach Documents
      </p>
      <p className="text-xs font-normal">
        Upload invoices or other supporting documents
      </p>
      {securityDeposit?.attach_files?.map((_f, index) => (
        <div
          key={index}
          className="border border-black p-2 my-2 rounded-md flex items-center justify-between"
        >
          <DocumentIcon className="w-4 h-4" />
          <p className="text-center max-w-[80%] truncate">{_f.file_name}</p>
          <SecurityDepositDeleteAttachedFileButton
            leaseId={leaseId}
            fileId={_f.object_uuid}
          />
        </div>
      ))}
      <SecurityDepositUploadDocument leaseId={leaseId} />
    </div>
  );
};

export default LeaseAttachedFiles;
