import { ChevronDownIcon } from "@heroicons/react/24/outline";

type Props = {
  title: string;
  children?: React.ReactNode;
};
const DropdownMenuItem = ({ title, children }: Props) => {
  return (
    <div className="dropdown">
      <div
        tabIndex={0}
        role="button"
        className="flex items-center text-[#8BABB6]"
      >
        <p className="text-sm p-4">{title}</p>
        <ChevronDownIcon className="w-4 h-4" />
      </div>
      <ul
        tabIndex={0}
        className="dropdown-content menu bg-base-100 rounded-lg z-[1] w-52 p-0 py-2 shadow"
      >
        {children}
      </ul>
    </div>
  );
};

export default DropdownMenuItem;
