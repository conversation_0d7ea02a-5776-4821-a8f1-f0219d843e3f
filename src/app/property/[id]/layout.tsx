import Sidebar from "@/app/components/rental-listings/Sidebar";
import ListingDetailsSection from "@/app/components/rental-listings/ListingDetailsSection";
import BreadcrumbContainer from "@/clients/views/rental-listings/BreadcrumbContainer";

export default function RentalsLayout({
  children,
  params: { id },
}: Readonly<{
  children: React.ReactNode;
  params: { id: string | string[] };
}>) {
  return (
    <>
      <Sidebar propertyId={Number(id)} />
      <div className="p-0 md:p-2 lg:p-4 lg:pl-[192px] pt-[52px] md:pt-5 lg:pt-5 pb-4 mb-4">
        <BreadcrumbContainer />
        <ListingDetailsSection id={Number(id)} />
        {children}
      </div>
    </>
  );
}
