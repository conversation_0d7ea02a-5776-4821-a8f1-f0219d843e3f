import { getUserProfile } from "@/app/actions/profile";
import { getPropertyDetails } from "@/app/actions/property";
import BookingsTable from "@/app/components/rental-listings/overview/BookingsTable";
import OverviewPhotosWrapper from "@/clients/views/rental-listings/overview/OverviewPhotosWrapper";
import { UserProfile } from "@/types/profile";
import { Property } from "@/types/property";
import Image from "next/image";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";
import PublishAndDistributionForm from "@/clients/views/rental-listings/overview/PublishAndDistributionForm";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

const ListingOverviewPage = async ({ params: { id } }: PageProps) => {
  const userData = getUserProfile<UserProfile>();
  const propertyDetails = getPropertyDetails<Property>(Number(id));

  const [data, propertyData] = await Promise.all([userData, propertyDetails]);

  if (!data.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }
  return (
    <>
      <div className="flex items-center justify-between p-2">
        <p className="text-sm font-bold">Overview</p>
        <div className="flex items-center gap-2">
          <Link
            href={`/${id}/photo`}
            className="bg-navy !text-xs text-white px-4 py-2 font-bold rounded hover:bg-[#0277BD]"
          >
            Manage Photos
          </Link>
        </div>
      </div>

      <div className="my-0 md:my-4 flex flex-col md:flex-row gap-4 p-2 md:p-0">
        {propertyData?.images.length > 0 && (
          <OverviewPhotosWrapper
            images={propertyData?.images?.map((_img) => _img.url) ?? []}
          >
            <div className="flex flex-wrap gap-2 mb-2">
              {(propertyData?.images ?? []).slice(0, 12).map((_img, index) => (
                <Image
                  key={index}
                  id={index.toString()}
                  alt="property image"
                  src={_img.small_url}
                  width={0}
                  height={0}
                  sizes="128px"
                  className="w-[calc(25%-6px)] md:w-[calc(25%-10px)] xl:w-[calc(20%-10px)] h-auto md:h-[80px] rounded-[10px] shadow-card cursor-pointer"
                />
              ))}
            </div>
          </OverviewPhotosWrapper>
        )}
      </div>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="w-full md:w-1/2 p-2 md:p-4 border rounded-[10px] h-min">
          <div className="px-0 py-2 flex items-center justify-between">
            <p className="text-sm font-bold">Bookings</p>
            <Link
              href={`/${Number(id)}/calendar`}
              className="bg-navy !text-xs text-white px-4 py-2 font-bold rounded hover:bg-[#0277BD]"
            >
              View Calendar
            </Link>
          </div>
          <hr className="my-2" />
          <Suspense
            fallback={
              <div className="h-[100px]">
                <span className="loading loading-spinner loading-md align-middle m-auto" />
              </div>
            }
          >
            <BookingsTable propertyId={Number(id)} />
          </Suspense>
        </div>

        {data?.is_admin && (
          <PublishAndDistributionForm
            propertyId={Number(id)}
            property={propertyData}
          />
        )}
      </div>
    </>
  );
};

export default ListingOverviewPage;
