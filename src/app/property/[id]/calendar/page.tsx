import { getCalendarNotes } from "@/app/actions/calendar";
import { getUserProfile } from "@/app/actions/profile";
import StatusPill from "@/app/components/calendar/StatusPill";
import CalendarHeader from "@/clients/views/calendar/CalendarHeader";
import CalendarWrapper from "@/clients/views/calendar/CalendarWrapper";
import ViewSelector from "@/clients/views/calendar/ViewSelector";
import ManageCalendarContextContainer from "@/clients/contexts/ManageCalendarContext";
import { BlockedType } from "@/types/calendar";
import { UserProfile } from "@/types/profile";

import classNames from "classnames";
import dayjs from "dayjs";
import { Suspense } from "react";
import LastUpdatedText from "@/app/components/calendar/LastUpdatedText";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import { Comment } from "@/types/service-providers";
import CalendarNoteActions from "@/clients/views/calendar/CalendarNoteActions";
import AddCalendarNoteButton from "@/clients/views/calendar/AddCalendarNoteButton";
import CalendarLinkForm from "@/clients/views/calendar/CalendarLinkForm";
import { getPropertyDetails } from "@/app/actions/property";
import { Property } from "@/types/property";
import ValidateButton from "@/clients/views/calendar/ValidateButton";
import CalendarSidebar from "@/clients/views/calendar/CalendarSidebar";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

type PageProps = {
  params: { id: string | string[] };
};

export default async function CalendarPage({
  // searchParams,
  params: { id },
}: PageProps) {
  const currentYear = dayjs();

  const userDataPromise = getUserProfile<UserProfile>();
  const propertyDataPromise = getPropertyDetails<Property>(Number(id));
  const notesPrmomise = getCalendarNotes<{
    count: number;
    next: string;
    results: Comment[];
  }>(Number(id), 0, 1);
  const [userData, { results }, propertyData] = await Promise.all([
    userDataPromise,
    notesPrmomise,
    propertyDataPromise,
  ]);

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  if (!propertyData) {
    notFound();
  }
  const availabilityData = propertyData?.availabilities ?? [];
  const rentalRates = propertyData?.rates ?? [];
  // const hasStartDate = !!searchParams?.selectionStart;

  return (
    <>
      <ManageCalendarContextContainer
        defaultYear={currentYear.year().toString()}
        rentalRates={rentalRates}
      >
        <div className="flex gap-4">
          <div
            id="calendar-left-pane"
            className={classNames(
              "h-[68vh] md:overflow-y-scroll w-full md:w-[calc(100%-210px)] xl:w-[calc(100%-260px)] p-2 md:p-4 md:pb-10 md:border rounded-lg flex flex-col gap-4",
              {
                // "md:w-full xl:w-full": !searchParams?.selectionStart,
              }
            )}
          >
            <p className="text-sm md:text-[19px] font-bold">Calendar</p>
            <div className="flex md:items-center flex-col md:flex-row justify-between gap-2">
              <Suspense fallback={"Loading"}>
                <LastUpdatedText propertyId={Number(id)} />
              </Suspense>
              <div>
                <ValidateButton propertyId={Number(id)} />
                <Link
                  href={`/${id}/calendar/update-logs?offset=0`}
                  className="text-xs border-black border px-4 py-2 font-bold rounded"
                >
                  View Update Log
                </Link>
              </div>
            </div>
            <CalendarLinkForm propertyId={Number(id)} />
            <div className="md:border p-2 md:p-4 rounded-lg">
              <div className="flex items-center justify-between pb-2">
                <p className="text-sm font-bold">Notes</p>
                <div className="flex items-center gap-2">
                  <Link
                    href={`/${id}/calendar/notes?offset=0`}
                    className="text-sm underline"
                  >
                    View all notes
                  </Link>
                  <AddCalendarNoteButton propertyId={Number(id)} />
                </div>
              </div>
              <div className="overflow-x-auto">
                <table className="table text-xs text-left">
                  <thead className="bg-white border-t">
                    <tr>
                      <th className="text-black-60 font-normal w-[10%]">
                        Date
                      </th>
                      <th className="text-black-60 font-normal w-[20%]">
                        Posted by
                      </th>
                      <th className="text-black-60 font-normal w-[70%]">
                        Note
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {results?.map((_comment, index) => (
                      <tr key={index}>
                        <td className="text-left align-top">
                          {dayjs(_comment.date).format("MM/DD/YYYY")}
                        </td>
                        <td className="text-left align-top">
                          {_comment.posted_by}
                        </td>
                        <td className="text-left align-top flex gap-4">
                          <p className="flex-grow">{_comment.content}</p>
                          {(userData.user_id === _comment.user ||
                            userData.is_admin) && (
                            <CalendarNoteActions
                              propertyId={Number(id)}
                              comment={_comment}
                            />
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            <div className="flex items-center justify-between gap-2">
              <CalendarHeader />
              <ViewSelector />
            </div>
            <div className="flex flex-center gap-2 md:gap-8">
              <StatusPill
                className="py-1 md:py-0"
                type={BlockedType.LEASED}
                title="C&C Lease"
              />
              <StatusPill
                className="py-1 md:py-0"
                type={BlockedType.OWNER_TIME}
                title="Owner Time"
              />
              <StatusPill
                className="py-1 md:py-0"
                type={BlockedType.OTHER}
                title="Other"
              />
            </div>
            <CalendarWrapper
              availabilities={availabilityData}
              rentalRates={rentalRates}
              propertyId={Number(id)}
            />
          </div>
          <CalendarSidebar
            availabilityData={availabilityData}
            propertyId={Number(id)}
            propertyData={propertyData}
          />
        </div>
      </ManageCalendarContextContainer>
    </>
  );
}
