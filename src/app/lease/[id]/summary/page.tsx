import { getLeaseComments, getLeaseDetails } from '@/app/actions/lease';
import { notFound } from 'next/navigation';
import { Lease, LeaseComment } from '@/types/lease';
import LeaseSidebar from '@/app/components/lease/LeaseSidebar';
import LeaseSecurityDeposit from '@/app/components/lease/financial-summary/LeaseSecurityDeposit';
import LeasePaymentItem from '@/app/components/lease/financial-summary/LeasePaymentItem';
import LeaseFinancialSummaryNotes from '@/clients/views/lease/financial-summary/LeaseFinancialSummaryNotes';
import LeaseAttachedFiles from '@/app/components/lease/financial-summary/LeaseAttachedFiles';
import LeaseDetailsSection from '@/app/components/lease/LeaseDetailsSection';

type PageProps = {
  params: { id: string | string[] };
};

const PaymentManagement = async ({ params: { id } }: PageProps) => {
  const leaseId = Array.isArray(id) ? Number(id[0]) : Number(id);
  const lease = await getLeaseDetails<Lease>(leaseId);
  const commentPayload = await getLeaseComments<{
    results: LeaseComment[];
  }>(leaseId);

  if (!lease) {
    notFound();
  }

  const showTwoCols = lease?.payments?.length === 1;

  return (
    <>
      <LeaseSidebar leaseId={leaseId} />
      <div className="p-0 md:p-4 md:pl-[192px] pt-[52px] md:pt-5 pb-4 mb-4">
        <LeaseDetailsSection lease={lease} />
        <div
          className={`grid grid-cols-1 md:grid-cols-2 ${
            !showTwoCols ? 'xl:grid-cols-3' : '2xl:grid-cols-3'
          } gap-4`}
        >
          {lease.payments.map((_p, index) => (
            <LeasePaymentItem
              key={_p.payment_uuid}
              payment={_p}
              isLast={index === lease.payments.length - 1}
            />
          ))}
          <div className="p-2 rounded-md bg-[#F7A2B6] h-min">
            <LeaseSecurityDeposit
              leaseId={leaseId}
              securityDeposit={lease.security_deposit}
            />
            <LeaseFinancialSummaryNotes
              comments={commentPayload?.results ?? []}
              leaseId={leaseId}
            />
            <LeaseAttachedFiles
              leaseId={leaseId}
              securityDeposit={lease.security_deposit}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default PaymentManagement;
