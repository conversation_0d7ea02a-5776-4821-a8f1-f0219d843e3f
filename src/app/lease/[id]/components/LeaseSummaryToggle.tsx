'use client';

import { useState } from 'react';
import classNames from 'classnames';

import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import SummaryDetails from './summary/SummaryDetails';

type LeaseSummaryToggleProps = {
  id: string | string[];
};

const LeaseSummaryToggle = ({ id }: LeaseSummaryToggleProps) => {
  const [open, setOpen] = useState(false);
  const leaseId = Array.isArray(id) ? Number(id[0]) : Number(id);

  return (
    <div className="flex flex-col gap-y-2">
      <div className="w-full flex bg-white items-center justify-between collapse-title px-4 py-2 !min-h-[36px] rounded-md">
        <button
          onClick={() => {
            setOpen(!open);
          }}
          className={classNames(
            'text-sm flex items-center w-full font-bold capitalize'
          )}
        >
          Lease General Information
          <div className="ml-auto mr-0 flex items-center">
            <ChevronDownIcon
              className={classNames('w-auto h-[14px]', {
                hidden: open,
              })}
            />
            <ChevronUpIcon
              className={classNames('w-auto h-[14px]', {
                hidden: !open,
              })}
            />
          </div>
        </button>
      </div>
      {open ? <SummaryDetails leaseId={leaseId} /> : null}
    </div>
  );
};

export default LeaseSummaryToggle;
