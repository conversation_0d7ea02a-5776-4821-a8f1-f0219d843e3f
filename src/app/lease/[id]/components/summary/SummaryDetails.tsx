import { Lease } from '@/types/lease';
import { getLeaseDetails } from '@/app/actions/lease';
import { KeyValue } from '../KeyValue';
import { useEffect, useState } from 'react';

type LeaseOrNull = Lease | null;

const useLeaseDetails = ({
  leaseId,
}: {
  leaseId: number;
}): { lease: LeaseOrNull } => {
  const [lease, setLease] = useState<LeaseOrNull | null>(null);

  useEffect(() => {
    if (leaseId) {
      getLeaseDetails(leaseId).then((response) => {
        if (response) {
          setLease(response as Lease);
        }
      });
    }
  }, [leaseId]);

  return {
    lease,
  };
};

const SummaryDetails = ({ leaseId }: { leaseId: number }) => {
  const { lease } = useLeaseDetails({ leaseId });

  if (!lease) {
    return <p className="text-white">Loading...</p>;
  }

  const generalInfo = {
    listing_address: lease?.listing_address ?? '',
    owner_full_name: `${lease?.owner_first_name ?? ''} ${
      lease?.owner_last_name ?? ''
    }`,
    tenant_full_name: `${lease?.tenant_first_name ?? ''} ${
      lease?.tenant_last_name ?? ''
    }`,
    arrival_departure: `${lease?.arrival_date} / ${lease?.departure_date}`,
  };

  return (
    <div className="flex flex-col gap-y-2">
      <KeyValue label="Address" value={generalInfo?.listing_address} />
      <KeyValue label="Landlord" value={generalInfo.owner_full_name} />
      <KeyValue label="Tenant" value={generalInfo?.tenant_full_name} />
      <KeyValue label="Arr/Dep." value={generalInfo?.arrival_departure} />
    </div>
  );
};

export default SummaryDetails;
