import React, { ChangeEvent } from 'react';
import { Field, FieldArray } from 'formik';

import Input from '@/clients/ui/input';
import Date from '@/clients/ui/date';
import classNames from 'classnames';
import {
  calculateGrandTotal,
  getFormFieldValidatedClassNames,
} from '@/app/lease/utils';

interface Payment {
  bill: {
    processing_fee: number | string;
    rent: number | string;
    security_deposit: number | string;
    other_fee: number | string;
  };
  due_date: string; // ISO string for the date
  occupancyTax: number | string;
}

export interface PaymentScheduleValues {
  payments: Payment[];
}

interface PaymentScheduleProps {
  values: any;
  errors: any;
  setFieldValue: any;
}

const PaymentSchedule: React.FC<PaymentScheduleProps> = ({
  values,
  errors,
  setFieldValue,
}) => {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex justify-between items-center md:justify-start md:gap-x-4">
        <label htmlFor="numberOfPayments" className="text-xs md:text-base">
          Number of Payments
        </label>
        <Field
          as="select"
          className="border p-2 rounded-md"
          value={values?.payments.length}
          onChange={(event: ChangeEvent<HTMLSelectElement>) => {
            const emptyPayment = {
              bill: {
                rent: 0,
                processing_fee: 0,
                other_fee: 0,
                security_deposit: 0,
              },
              occupancy_tax: 0,
              due_date: '',
            };
            const totalPayments = parseInt(event.target.value, 10); // Parse as integer

            const prevPayments = values.payments;
            const currentPaymentsLength = values.payments.length;
            let updatedPayments;

            if (totalPayments > currentPaymentsLength) {
              // Add new empty payments
              const newPayments = Array(
                totalPayments - currentPaymentsLength
              ).fill(emptyPayment);
              updatedPayments = [...prevPayments, ...newPayments];
            } else if (totalPayments < currentPaymentsLength) {
              // Remove existing payments
              updatedPayments = prevPayments.slice(0, totalPayments);
            } else {
              // No change needed
              updatedPayments = prevPayments;
            }

            setFieldValue('payments.payments', updatedPayments);
          }}
        >
          {[1, 2, 3, 4].map((num) => (
            <option key={num} value={num}>
              {num}
            </option>
          ))}
        </Field>
      </div>

      <FieldArray name="payments.payments">
        {() => (
          <div className="flex flex-col gap-y-4 md:grid md:grid-cols-3 md:gap-x-4">
            {values.payments.map((payment: any, index: number) => {
              return (
                <div
                  key={index}
                  className={classNames(
                    'border rounded p-4 bg-[#F6F6F6] px-2 py-4 rounded-md',
                    {
                      grow: values.payments.length >= 2,
                    }
                  )}
                >
                  <h3 className="font-bold">Payment {index + 1}</h3>
                  <div className="flex flex-col gap-y-2">
                    <div className="flex justify-between items-center">
                      <label className="text-xs md:text-base">
                        Payment Due
                      </label>
                      <Field
                        name={`payments.payments.${index}.due_date`}
                        className={getFormFieldValidatedClassNames(
                          errors?.payments?.[index]?.due_date,
                          'w-44'
                        )}
                        value={payment.due_date}
                        as={Date}
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <label className="text-xs md:text-base">Rent</label>
                      <Field
                        className={getFormFieldValidatedClassNames(
                          errors?.payments?.[index]?.rent,
                          'w-44'
                        )}
                        name={`payments.payments.${index}.bill.rent`}
                        as={Input}
                        value={Number(payment.bill.rent)}
                        type="number"
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <label className="text-xs md:text-base">
                        Processing Fee
                      </label>
                      <Field
                        className={getFormFieldValidatedClassNames(
                          errors?.payments?.[index]?.processing_fee,
                          'w-44'
                        )}
                        name={`payments.payments.${index}.bill.processing_fee`}
                        as={Input}
                        type="number"
                        value={Number(payment.bill.processing_fee)}
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <label className="text-xs md:text-base">Other Fees</label>
                      <Field
                        className={getFormFieldValidatedClassNames(
                          errors?.payments?.[index]?.bill?.other_fee,
                          'w-44'
                        )}
                        name={`payments.payments.${index}.bill.other_fee`}
                        value={Number(payment.bill.other_fee)}
                        as={Input}
                        type="number"
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <label className="text-xs md:text-base">
                        Occupancy Tax
                      </label>
                      <Field
                        className={getFormFieldValidatedClassNames(
                          errors?.payments?.[index]?.occupancyTax,
                          'w-44'
                        )}
                        name={`payments.payments.${index}.occupancyTax`}
                        as={Input}
                        type="number"
                        value={Number(payment.bill.tax)}
                        disabled
                      />
                    </div>
                    <div className="flex justify-between items-center">
                      <label className="text-xs md:text-base">
                        Security Deposit
                      </label>
                      <Field
                        className={getFormFieldValidatedClassNames(
                          errors?.payments?.[index]?.securityDeposit,
                          'w-44'
                        )}
                        name={`payments.payments.${index}.bill.security_deposit`}
                        as={Input}
                        type="number"
                        value={Number(payment.bill.security_deposit)}
                        disabled
                      />
                    </div>
                  </div>
                  <div className="flex flex-col gap-y-1 my-4">
                    <hr className="border-black" />
                    <hr className="border-black" />
                  </div>
                  <div className="flex justify-between mt-4 text-xs md:text-base">
                    <h3 className="font-bold">Grand Total</h3>
                    <p className="w-32 text-black">
                      ${calculateGrandTotal(payment).toFixed(2)}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </FieldArray>
    </div>
  );
};

export default PaymentSchedule;
