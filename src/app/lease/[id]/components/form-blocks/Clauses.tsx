import React from 'react';
import { Field } from 'formik';

import Textarea from '@/clients/ui/textarea';
import Checkbox from '@/clients/ui/checkbox';

export interface ClausesFormValues {
  noSmoking: boolean;
  petsAllowed: boolean;
  poolWaiver: boolean;
  showClause: boolean;
  roofWaiver: boolean;
  comment: string;
}

interface ClausesFormProps {
  values: ClausesFormValues;
  errors: any;
}

type FieldBlockProps = {
  label: string;
  name: string;
  value: string;
  as: any;
};

const ClausesForm: React.FC<ClausesFormProps> = ({ values, errors }) => {
  return (
    <div className="flex flex-col gap-y-2.5">
      {/* Clauses */}
      <div className="flex flex-col gap-y-2.5">
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="noSmoking" className="text-xs md:text-base">
            No Smoking
          </label>
          <Field
            id="noSmoking"
            name="clauses.noSmoking"
            as={Checkbox}
            checked={values.noSmoking}
            label="No Smoking"
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="petsAllowed" className="text-xs md:text-base">
            Pets Allowed
          </label>
          <Field
            id="petsAllowed"
            name="clauses.petsAllowed"
            as={Checkbox}
            checked={values.petsAllowed}
            label="Pets Allowed"
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="poolWaiver" className="text-xs md:text-base">
            Pool Waiver Required
          </label>
          <Field
            id="poolWaiver"
            name="clauses.poolWaiver"
            as={Checkbox}
            checked={values.poolWaiver}
            label="Pool Waiver Required"
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="showClause" className="text-xs md:text-base">
            Show Clause
          </label>
          <Field
            id="showClause"
            name="clauses.showClause"
            as={Checkbox}
            checked={values.showClause}
            label="Show Clause"
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="roofWaiver" className="text-xs md:text-base">
            Roof Walker Waiver Required
          </label>
          <Field
            id="roofWaiver"
            name="clauses.roofWaiver"
            as={Checkbox}
            checked={values.roofWaiver}
            label="Roof Walker Waiver Required"
          />
        </div>
      </div>

      <div className="md:mt-8">
        <div>
          <label htmlFor="comment" className="text-sm font-bold">
            Signature Page Additions
          </label>
          <p className="text-sm italic">
            *This text will be printed on the signature page.
          </p>
        </div>
        <Field
          id="comment"
          name="clauses.comment"
          as={Textarea}
          value={values.comment}
          className="w-full h-24"
          placeholder="Enter text here"
        />
        {errors?.comment && (
          <div className="error text-xs md:text-base text-red-500">
            {errors.comment}.
          </div>
        )}
      </div>
    </div>
  );
};

export default ClausesForm;
