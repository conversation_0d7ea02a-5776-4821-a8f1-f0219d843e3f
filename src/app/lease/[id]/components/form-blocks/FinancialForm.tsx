import React from 'react';
import { Field, FieldArray } from 'formik';
import Input from '@/clients/ui/input';
import Checkbox from '@/clients/ui/checkbox';
import {
  calculateGrandTotal,
  getFormFieldValidatedClassNames,
} from '@/app/lease/utils';

type FinancialFormProps = {
  values: FinancialFormValues;
  errors: any;
};

interface Fee {
  reason: string;
  amount: number;
  taxable: boolean;
  custom?: boolean;
}

interface OccupancyTax {
  amount: number | string;
  exempt: boolean;
}

export interface FinancialFormValues {
  commission?: number | string;
  rent: number | string;
  processingFee: number | string;
  otherFees: Fee[];
  occupancyTax: OccupancyTax;
  securityDeposit: number | string;
}

const FinancialForm: React.FC<FinancialFormProps> = ({ values, errors }) => {
  return (
    <div className="flex flex-col gap-y-2">
      <div className="flex flex-col gap-y-2 md:gap-y-1">
        <div className="flex justify-between">
          <label className="text-xs md:text-base">Commission %</label>
          <Field
            name="financialInfo.commission"
            className={getFormFieldValidatedClassNames(errors?.commission)}
            as={Input}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <label className="text-xs md:text-base">Rent</label>
          <Field
            name="financialInfo.rent"
            className={getFormFieldValidatedClassNames(errors?.rent)}
            as={Input}
            type="number"
          />
        </div>
        <div className="flex justify-between">
          <label className="text-xs md:text-base">Processing Fee</label>
          <Field
            name="financialInfo.processingFee"
            className={getFormFieldValidatedClassNames(errors?.processingFee)}
            as={Input}
            type="number"
            min={0}
          />
        </div>
      </div>
      <div>
        <FieldArray name="financialInfo.otherFees">
          {({ push }) => (
            <>
              <label className="text-xs md:text-base">Other Fees</label>
              <div className="pl-4 mt-2 flex flex-col gap-y-2">
                {values.otherFees.map((fee, index) => (
                  <div
                    key={index}
                    className="flex justify-around md:justify-between items-center"
                  >
                    <div className="flex gap-x-10">
                      <Field
                        borderless={!fee.custom}
                        disabled={!fee.custom}
                        name={`financialInfo.otherFees.${index}.reason`}
                        as={Input}
                        placeholder="Fee Name"
                      />

                      <div className="flex gap-x-2 items-center">
                        <label
                          htmlFor={`otherFees.${index}.taxable`}
                          className="text-xs md:text-base"
                        >
                          <span className="text-[10px] md:text-xs">
                            Taxable
                          </span>
                        </label>
                        <Field
                          id={`otherFees.${index}.taxable`}
                          name={`financialInfo.otherFees.${index}.taxable`}
                          as={Checkbox}
                          type="checkbox"
                        />
                      </div>
                    </div>
                    <Field
                      className="w-20"
                      name={`financialInfo.otherFees.${index}.amount`}
                      as={Input}
                      type="number"
                    />
                  </div>
                ))}

                <button
                  type="button"
                  className="py-2 inline-block ml-0 mr-auto text-xs md:text-base"
                  onClick={() =>
                    push({
                      name: '',
                      taxable: false,
                      amount: '',
                      custom: true,
                    })
                  }
                >
                  + Add Other Fee
                </button>
              </div>
            </>
          )}
        </FieldArray>
      </div>
      <div className="flex justify-between items-center">
        <label htmlFor="amount" className="text-xs md:text-base">
          Occupancy Tax
        </label>

        <div className="flex gap-x-2 items-center">
          <label htmlFor="exempt" className="text-[10px] md:text-xs">
            Exempt
          </label>
          <Field
            id="exempt"
            name="financialInfo.occupancyTax.exempt"
            as={Checkbox}
            type="checkbox"
          />
        </div>
        <Field
          id="amount"
          className="w-32"
          name="financialInfo.occupancyTax.amount"
          as={Input}
          type="number"
        />
      </div>
      <div className="flex justify-between items-center">
        <label htmlFor="securityDeposit" className="text-xs md:text-base">
          Security Deposit
        </label>
        <Field
          id="securityDeposit"
          className="w-32"
          name="financialInfo.securityDeposit"
          as={Input}
          type="number"
        />
      </div>
      <div className="my-4">
        <hr />
      </div>
      <div className="flex justify-between text-sm md:text-base">
        <h3 className="font-bold">Grand Total</h3>
        <Input
          className="w-32 text-black font-bold"
          name="total"
          value={`${calculateGrandTotal(values).toFixed(2)}`}
        />
      </div>
    </div>
  );
};

export default FinancialForm;
