import classNames from 'classnames';

type KeyValueProps = {
  label: string;
  value?: string;
  className?: string;
};

export const KeyValue = ({ label, value, className }: KeyValueProps) => {
  if (!value) {
    return null;
  }

  return (
    <div className="flex items-center text-left gap-x-2 text-white text-xs md:bg-white md:text-[#2C3E50] rounded-md md:flex-wrap md:px-4 md:py-2 md:text-sm">
      <h4 className={classNames('min-w-24 md:min-w-0', className)}>{label}:</h4>
      <p className="py-1 px-1.5 font-bold border-solid border border-white rounded-lg">
        {value}
      </p>
    </div>
  );
};
