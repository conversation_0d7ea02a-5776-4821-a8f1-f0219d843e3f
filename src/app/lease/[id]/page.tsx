import { Suspense } from 'react';

import { getLeaseDetails } from '@/app/actions/lease';
import { Lease } from '@/types/lease';

import { notFound, redirect } from 'next/navigation';
import { LeaseContextContainer } from '@/clients/contexts/LeaseContext';
import FormikWrapper from '@/clients/views/lease/lease-details/FormikWrapper';
import LeaseSidebar from '@/app/components/lease/LeaseSidebar';
import { getUserProfile } from '@/app/actions/profile';
import { UserProfile } from '@/types/profile';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '';

type PageProps = {
  params: { id: string | string[] };
};

const LeaseDetailsPage = async ({ params: { id } }: PageProps) => {
  const leaseId = Array.isArray(id) ? Number(id[0]) : Number(id);
  const lease = await getLeaseDetails<Lease>(leaseId);
  const userData = await getUserProfile<UserProfile>();

  if (!lease) {
    notFound();
  }

  if (!userData.user_id) {
    redirect(BASE_URL);
    return;
  }

  return (
    <LeaseContextContainer lease={lease}>
      <LeaseSidebar leaseId={leaseId} />
      <div className="p-0 md:p-4 md:pl-[192px] pt-[52px] md:pt-5 pb-4 mb-4">
        <main className="min-h-screen">
          <Suspense fallback={<div className="p-8 py-[60px]">Loading...</div>}>
            <FormikWrapper
              leaseId={leaseId}
              lease={lease}
              userData={userData}
            />
          </Suspense>
        </main>
      </div>
    </LeaseContextContainer>
  );
};

export default LeaseDetailsPage;
