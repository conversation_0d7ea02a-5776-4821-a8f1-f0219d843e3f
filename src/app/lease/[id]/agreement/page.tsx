import { getLeaseDetails } from '@/app/actions/lease';
import { Lease } from '@/types/lease';
import { notFound } from 'next/navigation';
import LeaseSidebar from '@/app/components/lease/LeaseSidebar';
import Header from '@/clients/views/lease/agreement/Header';
import Agreement from '@/clients/views/lease/agreement/Agreement';
import SignatureInfoBlock from '@/clients/views/lease/agreement/SignatureInfoBlock';
import { LeaseUpload } from '@/clients/views/lease/agreement/LeaseUpload';
import LeaseDetailsSection from '@/app/components/lease/LeaseDetailsSection';

type PageProps = {
  params: { id: string | string[] };
};

const tenantSignedAt = (lease: Lease) => {
  if (
    lease.sign_info &&
    lease.sign_info.signature_request.signatures[0].signed_at
  ) {
    return lease.sign_info.signature_request.signatures[0].signed_at;
  } else {
    return null;
  }
};

const ownerSignedAt = (lease: Lease) => {
  if (
    lease.sign_info &&
    lease.sign_info.signature_request.signatures[1].signed_at
  ) {
    return lease.sign_info.signature_request.signatures[1].signed_at;
  } else {
    return null;
  }
};

const ensureArray = (candidateArr?: any[]) => {
  if (!candidateArr || !Array.isArray(candidateArr)) {
    return [];
  }

  return candidateArr;
};

const LeaseAgreement = async ({ params: { id } }: PageProps) => {
  const leaseId = Array.isArray(id) ? Number(id[0]) : Number(id);
  const lease = await getLeaseDetails<Lease>(leaseId);

  if (!lease) {
    notFound();
  }

  const tenantSignatureRequests = ensureArray(
    lease?.sign_info?.signature_request.signatures[0]?.sent_timestamps
  );
  const ownerSignatureRequests = ensureArray(
    lease?.sign_info?.signature_request.signatures[1]?.sent_timestamps
  );

  const ownerSigned = ownerSignedAt(lease);
  const tenantSigned = tenantSignedAt(lease);

  return (
    <>
      <LeaseSidebar leaseId={leaseId} />
      <div className="p-0 md:p-4 md:pl-[192px] pt-[52px] md:pt-4 pb-4 mb-4 grid grid-cols-1 xl:grid-cols-4 gap-4">
        <div className="col-span-3">
          <LeaseDetailsSection lease={lease} />
          <div className="p-4 border border-solid border-outline rounded-md w-full md:text-lg">
            <Header leaseId={leaseId} />
            {lease?.sign_file_info?.url ? (
              <Agreement fileUrl={lease?.sign_file_info?.url} />
            ) : (
              <div className="h-[250px] md:h-[530px] border border-outline rounded-[25px] flex items-center justify-center">
                <p className="text-gray-500">No PDF document available</p>
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-4">
          <SignatureInfoBlock
            type="Homeowner"
            signedAt={ownerSigned}
            lease={lease}
            sent={ownerSignatureRequests}
            uploadedAt={lease.updated_at}
          />
          <SignatureInfoBlock
            type="Tenant"
            signedAt={tenantSigned}
            lease={lease}
            sent={tenantSignatureRequests}
            uploadedAt={lease.updated_at}
          />

          <LeaseUpload leaseId={leaseId.toString()} />
        </div>
      </div>
    </>
  );
};

export default LeaseAgreement;
