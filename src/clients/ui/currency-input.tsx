'use client';

import { memo, useCallback } from 'react';
import FormHelperText from '@/app/ui/form-helper-text';
import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  wrapperclassName?: string;
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
  iconClassName?: string;
  isStringValue?: boolean;
  name?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
} & React.HTMLProps<HTMLInputElement>;

const CurrencyInput = ({
  label = '',
  required,
  error,
  helperText,
  className,
  wrapperclassName = '',
  iconClassName = '',
  placeholder,
  value,
  isStringValue,
  name,
  onChange,
  ...props
}: Props) => {
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        // Remove commas for processing
        const rawValue = e.target.value.replace(/,/g, '');

        // Only proceed if it's a valid number or empty
        if (rawValue === '' || /^\d*\.?\d*$/.test(rawValue)) {
          // Create a new synthetic event with the processed value
          const syntheticEvent = {
            ...e,
            target: {
              ...e.target,
              name: e.target.name,
              value: rawValue, // Pass the raw number without commas
            },
            currentTarget: {
              ...e.currentTarget,
              name: e.currentTarget.name,
              value: rawValue, // Pass the raw number without commas
            },
          };

          onChange(syntheticEvent as any);
        }
      }
    },
    [onChange]
  );

  // Format the display value with commas
  const displayValue = isStringValue
    ? value
    : typeof value === 'number' || (typeof value === 'string' && value !== '')
    ? Number(value).toLocaleString('en-US')
    : '';

  return (
    <div className={twMerge('relative', wrapperclassName)}>
      {!isStringValue && (
        <span
          className={twMerge(
            classNames('text-sm font-bold', {
              'input-disabled': props?.disabled,
            }),
            iconClassName
          )}
        >
          $
        </span>
      )}

      <input
        {...props}
        name={name}
        value={displayValue}
        onChange={handleChange}
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'input input-bordered h-min min-h-min rounded focus:outline-none p-4 text-right',
            error && 'border-error'
          ),
          className
        )}
        inputMode="numeric"
        autoComplete="off"
      />
      {helperText && (
        <div className="ml-2 absolute">
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default memo(CurrencyInput);
