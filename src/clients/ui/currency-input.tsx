'use client';

import { memo, useCallback } from 'react';
import FormHelperText from '@/app/ui/form-helper-text';
import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  wrapperclassName?: string;
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
  iconClassName?: string;
  isStringValue?: boolean;
  name?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
} & React.HTMLProps<HTMLInputElement>;

const CurrencyInput = ({
  label = '',
  required,
  error,
  helperText,
  className,
  wrapperclassName = '',
  iconClassName = '',
  placeholder,
  value,
  isStringValue,
  name,
  onChange,
  ...props
}: Props) => {
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (/^(\d+\.?\d*|\.\d*)$/.test(e.target.value)) {
        onChange?.(e as any);
      }
    },
    [onChange]
  );

  return (
    <div className={twMerge('relative', wrapperclassName)}>
      {!isStringValue && (
        <span
          className={twMerge(
            classNames('text-sm font-bold', {
              'input-disabled': props?.disabled,
            }),
            iconClassName
          )}
        >
          $
        </span>
      )}

      <input
        {...props}
        name={name}
        value={value}
        onChange={handleChange}
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'input input-bordered h-min min-h-min rounded focus:outline-none p-4 text-right',
            error && 'border-error'
          ),
          className
        )}
        inputMode="numeric"
        autoComplete="off"
      />
      {helperText && (
        <div className="ml-2 absolute">
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default memo(CurrencyInput);
