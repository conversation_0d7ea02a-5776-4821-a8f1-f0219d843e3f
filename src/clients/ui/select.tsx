'use client';
import { Nullable } from '@/types/common';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { useEffect, useMemo, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';

type Props = {
  placeholder?: string;
  className?: string;
  bodyClassName?: string;
  name?: string;
  options?: {
    id: number | string;
    name: string;
  }[];
  value?: Nullable<string | number>;
  onChange?: (
    value: { id: string | number; name: string },
    name?: string
  ) => void;
  disabled?: boolean;
};

const Select = ({
  className = '',
  bodyClassName = '',
  options = [],
  placeholder = '',
  value,
  onChange,
  name,
  disabled = false,
}: Props) => {
  const [isUpwardDir, setIsUpwardDir] = useState<boolean>(false);
  const elementRef = useRef<null | HTMLDivElement>(null);
  const containerRef = useRef<null | HTMLDivElement>(null);
  const selectedRef = useRef<null | HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const selected = useMemo(
    () => options?.find((_o) => _o.id === value),
    [options, value]
  );

  useEffect(() => {
    if (elementRef && isOpen) {
      setIsUpwardDir(
        (elementRef.current?.getBoundingClientRect().top ?? 0) >
          (2 * window.innerHeight) / 3
      );
    }
  }, [value, isOpen]);

  useEffect(() => {
    if (selectedRef && containerRef && isOpen) {
      containerRef?.current?.scrollTo(0, selectedRef.current?.offsetTop ?? 0);
    }
  }, [isOpen]);

  return (
    <>
      <div
        ref={elementRef}
        className={classNames(
          twMerge(
            'relative px-4 py-2 border rounded-md flex items-center justify-between cursor-pointer',
            disabled ? 'bg-gray-100 cursor-not-allowed opacity-70' : '',
            className
          )
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <p className="w-[90%]">{selected?.name ?? placeholder}</p>
        {!disabled &&
          (isOpen ? (
            <ChevronUpIcon className="w-auto h-[14px]" />
          ) : (
            <ChevronDownIcon className="w-auto h-[14px]" />
          ))}
        {isOpen && !disabled && (
          <>
            <div
              ref={containerRef}
              className={twMerge(
                classNames(
                  'absolute left-0 right-0 bg-white shadow-card py-2 rounded-md z-[999]',
                  {
                    'top-[40px]': !isUpwardDir,
                    'bottom-[40px]': isUpwardDir,
                  }
                ),
                bodyClassName
              )}
            >
              {options?.map((_option, index) => (
                <div
                  ref={selected?.id === _option.id ? selectedRef : null}
                  key={index}
                  onClick={(e) => onChange?.(_option, name)}
                  className={classNames(
                    'hover:bg-gray-100 py-2 pl-4 text-left font-normal',
                    {
                      'text-carolina-blue font-medium':
                        selected?.id === _option.id,
                    }
                  )}
                >
                  {_option.name}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
      {isOpen && (
        <div
          className="drawer-overlay fixed inset-0 z-[99]"
          onClick={() => setIsOpen(!isOpen)}
        />
      )}
    </>
  );
};

export default Select;
