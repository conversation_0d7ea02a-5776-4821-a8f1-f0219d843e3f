"use client";

import { FormEvent, useCallback } from "react";
import { FormValues } from "./GeneralInfoFormWrapper";
import Input from "@/clients/ui/input";
import Select from "@/clients/ui/select";
import CurrencyInput from "@/clients/ui/currency-input";

const CAPACITIES = Array(100)
  .fill(1)
  .map((_, index) => ({ id: index + 1, name: (index + 1).toString() }));

export const PETS = [
  {
    id: "true",
    name: "Yes",
  },
  {
    id: "false",
    name: "No",
  },
  {
    id: "ask_owner",
    name: "Ask Owner",
  },
];

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
};

const HouseRulesForm = ({ formState, onChange, errors }: Props) => {
  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeCapacity = useCallback(
    (capacity: { id: any; name: string }) => {
      onChange(capacity.id, "capacity");
    },
    [onChange]
  );

  const onChangePetRule = useCallback(
    (capacity: { id: any; name: string }) => {
      onChange(capacity.id, "pet_allow");
    },
    [onChange]
  );

  const onChangeCurrency = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, "");
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold">House Rules</p>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Max Guests</p>
        <Select
          className="text-xs font-bold w-[50%] px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          placeholder="Max Guests"
          options={CAPACITIES}
          value={formState?.capacity}
          onChange={onChangeCapacity}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Minimum Night Stay</p>
        <div className="w-[50%]">
          <Input
            name="min_night_stay"
            className="text-xs p-2 w-full"
            placeholder="Minimum Night Stay"
            value={formState?.min_night_stay ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.min_night_stay ?? ""}
            error={!!errors?.min_night_stay?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Pets</p>
        <Select
          className="text-xs font-bold w-[50%] px-2 py-2"
          bodyClassName="max-h-[200px] overflow-y-scroll"
          placeholder="Please chose"
          options={PETS}
          value={formState?.pet_allow}
          onChange={onChangePetRule}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Pet Fee</p>
        <CurrencyInput
          name="pet_fee"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="Pet Fee"
          value={formState?.pet_fee ?? ""}
          onChange={onChangeCurrency}
          helperText={errors?.pet_fee ?? ""}
          error={!!errors?.pet_fee?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Cleaning Fee</p>
        <CurrencyInput
          name="cleaning_fee"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="Cleaning Fee"
          value={formState?.cleaning_fee ?? ""}
          onChange={onChangeCurrency}
          helperText={errors?.cleaning_fee ?? ""}
          error={!!errors?.cleaning_fee?.length}
        />
      </div>
    </>
  );
};

export default HouseRulesForm;
