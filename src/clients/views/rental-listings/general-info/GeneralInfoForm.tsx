"use client";

import { FormEvent, useCallback } from "react";
import { FormValues } from "./GeneralInfoFormWrapper";
import Input from "@/clients/ui/input";
import Select from "@/clients/ui/select";
import { PUBLISH_TYPE } from "../overview/PublishAndDistributionForm";

type Props = {
  formState: FormValues;
  onChange: (value: any, name: string) => void;
  errors: any;
};

const GeneralInfoForm = ({ formState, onChange, errors }: Props) => {
  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onChangeSelect = useCallback(
    (value: { id: number | string; name: string }, name?: string) => {
      onChange(value.id, name ?? "");
    },
    [onChange]
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, "");
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  return (
    <>
      <p className="text-sm font-bold">House Information</p>
      <div className="py-2 flex items-start gap-2 w-full">
        <p className="w-[50%] text-xs pt-2">Key Number</p>
        <div className="w-[50%]">
          <Input
            name="key_number"
            className="text-xs p-2 w-full"
            placeholder="Key Number"
            rows={3}
            value={formState?.key_number ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.key_number ?? ""}
            error={!!errors?.key_number?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">House Phone Number</p>
        <div className="w-[50%]">
          <Input
            name="house_phone"
            className="text-xs p-2 w-full"
            placeholder="Phone"
            value={formState?.house_phone ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.house_phone ?? ""}
            error={!!errors?.house_phone?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">MA Occupancy Tax Certificate Number</p>
        <div className="w-[50%]">
          <Input
            name="occupancy_tax_number"
            className="text-xs p-2 w-full"
            placeholder="-"
            value={formState?.occupancy_tax_number ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.occupancy_tax_number ?? ""}
            error={!!errors?.occupancy_tax_number?.length}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Nantucket STR Permit</p>
        <div className="w-[50%]">
          <Input
            name="short_term_rental_permit_number"
            className="text-xs p-2 w-full"
            placeholder="-"
            value={formState?.short_term_rental_permit_number ?? ""}
            onChange={onChangeTextInput}
            helperText={errors?.short_term_rental_permit_number ?? ""}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Year Built</p>
        <div className="w-[50%]">
          <Input
            name="year_built"
            className="text-xs p-2 w-full"
            placeholder="Year Built"
            value={formState?.year_built ?? ""}
            onChange={onChangeNumeric}
            helperText={errors?.year_built ?? ""}
            error={!!errors?.year_built?.length}
            pattern="\d*"
            inputMode="numeric"
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Year Renovated</p>
        <div className="w-[50%]">
          <Input
            name="year_renovated"
            className="text-xs p-2 w-full"
            placeholder="Year Renovated"
            value={formState?.year_renovated ?? ""}
            onChange={onChangeNumeric}
            helperText={errors?.year_renovated ?? ""}
            error={!!errors?.year_renovated?.length}
            pattern="\d*"
            inputMode="numeric"
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Listing Status</p>
        <div className="w-[50%]">
          <Select
            name="publish_type"
            value={formState.publish_type}
            className="text-xs font-bold px-4 py-2"
            onChange={onChangeSelect}
            options={[
              {
                name: "Publish",
                id: "publish",
              },
              {
                name: "Hide from Public",
                id: "hide",
              },
              {
                name: "Deactivate",
                id: "deactivate",
              },
            ]}
          />
        </div>
      </div>
      {formState.publish_type === PUBLISH_TYPE.DEACTIVATE && (
        <div className="mb-2">
          <p className="text-xs mb-1">Reason for deactivation</p>
          <Input
            className="text-xs p-2 w-full"
            name="deactivated_reason"
            value={formState?.deactivated_reason ?? ""}
            onChange={onChangeTextInput}
            placeholder="Deactivated reason"
            helperText={errors.deactivated_reason}
            error={!!errors.deactivated_reason}
          />
        </div>
      )}
    </>
  );
};

export default GeneralInfoForm;
