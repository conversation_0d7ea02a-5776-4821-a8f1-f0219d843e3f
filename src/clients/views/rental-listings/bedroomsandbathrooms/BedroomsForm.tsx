"use client";

import Button from "@/clients/ui/button";
import Select from "@/clients/ui/select";
import { ProgressStatus } from "@/types/common";
import { ItemType, Property } from "@/types/property";
import { BedroomPayload, formatBedroomsForForm } from "@/utils/bedrooms";
import { useCallback, useEffect, useState } from "react";
import BedroomItemForm from "./BedroomItemForm";
import useSWR from "swr";
import fetcher from "@/utils/swr/fetcher";
import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import toast from "react-hot-toast";

export const SELECT_OPTIONS = Array(20)
  .fill(1)
  .map((_, index) => ({ id: index + 1, name: (index + 1).toString() }));

type Props = {
  property: Property;
};

export type BedroomFormErrors = {
  [key: number]: { floor_level: string; beds: { [key: number]: string } };
};

const BedroomsForm = ({ property }: Props) => {
  const { data: floorLevelTypes, isLoading } = useSWR("enum-floor-level", () =>
    fetcher<ItemType[]>("enum-floor-level")
  );
  const { data: bedTypes, isLoading: isLoadingBedTypes } = useSWR(
    "enum-bed-type",
    () => fetcher<ItemType[]>("enum-bed-type")
  );
  const [noOfBedrooms, setNoOfBedrooms] = useState<number>(
    property.bedroom_number ?? 1
  );

  const [bedrooms, setBedrooms] = useState<BedroomPayload[]>([]);
  const [errors, setErrors] = useState<BedroomFormErrors>([]);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onChangeBedroomCount = useCallback((value: any) => {
    setNoOfBedrooms(value.id);
  }, []);

  const onChangeBedroomData = useCallback(
    (index: number, data: BedroomPayload) => {
      setBedrooms([
        ...bedrooms.slice(0, index),
        data,
        ...bedrooms.slice(index + 1),
      ]);
    },
    [bedrooms]
  );

  const preSubmitCheck = useCallback(() => {
    let errors: BedroomFormErrors = {};
    bedrooms.map((_bdRoom, _index) => {
      if (!_bdRoom.floor_level) {
        errors[_index] = {
          ...errors[_index],
          floor_level: "Floor level is required",
        };
      }

      _bdRoom.beds.map((_bed, _bdIdx) => {
        if (!_bed.type) {
          errors[_index] = {
            ...errors[_index],
            beds: {
              ...errors[_index]?.beds,
              [_bdIdx]: "Bed type is required",
            },
          };
        }
      });
    });
    setErrors(errors);
    return errors;
  }, [bedrooms]);

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    console.log("errors", _errors);
    if (Object.values(_errors).length > 0) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      bedrooms,
      bedroom_number: noOfBedrooms,
    };
    updatePropertyDetails(property.listing_id, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [bedrooms, noOfBedrooms, preSubmitCheck, property.listing_id]);

  useEffect(() => {
    const newBeds = Array(noOfBedrooms)
      .fill(1)
      .map((_bd, index) => {
        if (!!bedrooms[index]) {
          return bedrooms[index];
        }

        return {
          floor_level: null,
          beds: [
            {
              type: null,
              number: 1,
            },
          ],
        };
      });
    setBedrooms(newBeds);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [noOfBedrooms]);

  useEffect(() => {
    setBedrooms(formatBedroomsForForm(property?.bedrooms ?? []));
  }, [property?.bedrooms]);

  return (
    <>
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Bedrooms</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">How many bedrooms</p>
        <div className="w-[50%]">
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            placeholder="Please select"
            options={SELECT_OPTIONS}
            value={noOfBedrooms}
            onChange={onChangeBedroomCount}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 gap-4">
        {bedrooms.map((_bedroom, index) => (
          <BedroomItemForm
            key={index}
            index={index}
            bedroomData={_bedroom}
            errors={errors}
            floorLevelTypes={floorLevelTypes ?? []}
            bedTypes={bedTypes ?? []}
            onChangeBedroomData={onChangeBedroomData}
          />
        ))}
      </div>
    </>
  );
};

export default BedroomsForm;
