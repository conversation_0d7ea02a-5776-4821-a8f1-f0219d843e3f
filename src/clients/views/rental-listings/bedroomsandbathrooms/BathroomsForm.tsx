"use client";

import Button from "@/clients/ui/button";
import Select from "@/clients/ui/select";
import { ProgressStatus } from "@/types/common";
import { ItemType, Property } from "@/types/property";
import { useCallback, useEffect, useState } from "react";
import { SELECT_OPTIONS } from "./BedroomsForm";
import { BathroomPayload, formatBathroomsForForm } from "@/utils/bathrooms";
import BathroomItemForm from "./BathroomItemForm";
import useSWR from "swr";
import fetcher from "@/utils/swr/fetcher";
import { updatePropertyDetails } from "@/app/actions/property";
import { revalidateTagByName } from "@/app/actions/revalidateTag";
import toast from "react-hot-toast";

type Props = {
  property: Property;
};

export type BathroomFormErrors = {
  [key: number]: { attach_bedroom: string };
};

const BathroomsForm = ({ property }: Props) => {
  const { data: bathroomTypes, isLoading } = useSWR("enum-bathroom-type", () =>
    fetcher<ItemType[]>("enum-bathroom-type")
  );
  const [bathroomNumber, setBathroomNumber] = useState<number>(
    property.bathroom_number ?? 1
  );
  const [halfBathroomNumber, setHalfBathroomNumber] = useState<number>(
    property.half_bathroom_number ?? 1
  );

  const [bathrooms, setBathrooms] = useState<BathroomPayload[]>([]);
  const [errors, setErrors] = useState<BathroomFormErrors>([]);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );

  const onChangeBathroomCount = useCallback((value: any) => {
    setBathroomNumber(value.id);
  }, []);

  const onChangeHalfBathroomCount = useCallback((value: any) => {
    setHalfBathroomNumber(value.id);
  }, []);

  const onChangeBathroomData = useCallback(
    (index: number, data: BathroomPayload) => {
      setBathrooms([
        ...bathrooms.slice(0, index),
        data,
        ...bathrooms.slice(index + 1),
      ]);
    },
    [bathrooms]
  );

  const preSubmitCheck = useCallback(() => {
    let errors: BathroomFormErrors = {};
    bathrooms.map((_bdRoom, _index) => {
      if (_bdRoom.checked && !_bdRoom.bedroom) {
        errors[_index] = {
          attach_bedroom: "Associated Bedroom is required",
        };
      }
    });
    setErrors(errors);
    return errors;
  }, [bathrooms]);

  const onSave = useCallback(() => {
    const _errors = preSubmitCheck();
    console.log("errors", _errors, Object.values(_errors));
    if (Object.values(_errors).length > 0) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      bathrooms,
      bathroom_number: bathroomNumber,
      half_bathroom_number: halfBathroomNumber,
    };
    updatePropertyDetails(property.listing_id, payload)
      .then((data) => {
        revalidateTagByName(`property-details-${property.listing_id}`);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success("Property details successfully updated!");
      })
      .catch((err) => {
        console.log("error is", err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [
    bathroomNumber,
    bathrooms,
    halfBathroomNumber,
    preSubmitCheck,
    property.listing_id,
  ]);

  useEffect(() => {
    const newBathrooms = Array(bathroomNumber)
      .fill(1)
      .map((_bd, index) => {
        if (!!bathrooms[index]) {
          return bathrooms[index];
        }

        return {
          bidet: false,
          toilet: false,
          tub: false,
          combination_tub: false,
          jetted_tub: false,
          shower: false,
          outdoor_shower: false,
          bedroom: null,
          type: null,
        };
      });
    setBathrooms(newBathrooms);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bathroomNumber]);

  useEffect(() => {
    setBathrooms(formatBathroomsForForm(property?.bathrooms ?? []));
  }, [property?.bathrooms]);

  return (
    <>
      <div className="px-0 py-2 flex items-center justify-between">
        <p className="text-sm font-bold">Bathrooms</p>
        <Button
          onClick={onSave}
          className="!text-xs min-w-[120px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Save / Update
        </Button>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">How many bathrooms</p>
        <div className="w-[50%]">
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            placeholder="Please select"
            options={SELECT_OPTIONS}
            value={bathroomNumber}
            onChange={onChangeBathroomCount}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">How many half bathrooms</p>
        <div className="w-[50%]">
          <Select
            className="text-xs font-bold w-full px-2 py-2"
            bodyClassName="max-h-[200px] overflow-y-scroll"
            placeholder="Please select"
            options={SELECT_OPTIONS}
            value={halfBathroomNumber}
            onChange={onChangeHalfBathroomCount}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 gap-4">
        {bathrooms.map((_bath, index) => (
          <BathroomItemForm
            key={index}
            index={index}
            bathroomData={_bath}
            onChangeBathroomData={onChangeBathroomData}
            bathroomTypes={bathroomTypes ?? []}
            errors={errors}
            property={property}
          />
        ))}
      </div>
    </>
  );
};

export default BathroomsForm;
