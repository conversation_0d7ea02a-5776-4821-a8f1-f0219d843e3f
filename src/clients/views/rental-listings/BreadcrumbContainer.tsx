"use client";

import Breadcrumb from "@/app/ui/breadcrumb";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "";

const BreadcrumbContainer = () => {
  const pathname = usePathname();
  const tabName = useMemo(() => {
    const name = pathname.split("/")[2].replace("-", " ");
    switch (name) {
      case "general":
        return "General Info";

      case "bedroomsandbathrooms":
        return "Rooms";

      case "service":
        return "Service Provides";

      default:
        return name;
    }
  }, [pathname]);

  return (
    <Breadcrumb
      className="hidden md:flex"
      elements={[
        { id: 1, title: "Rentals", link: `${BASE_URL}/properties` },
        {
          id: 2,
          title: "Rental Listings",
          link: `${BASE_URL}/properties`,
        },
        {
          id: 3,
          title: tabName,
          link: `/${pathname}`,
        },
      ]}
    />
  );
};

export default BreadcrumbContainer;
