"use client";

import classNames from "classnames";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { twMerge } from "tailwind-merge";

type Props = {
  href: string;
  title: string;
  className?: string;
  pagePath: string;
};

const SidebarNavItem = ({ href, title, className = "", pagePath }: Props) => {
  const pathname = usePathname();

  return (
    <Link
      href={href}
      className={twMerge(
        classNames("px-4 py-2 text-sm", {
          "bg-white rounded-[10px] rounded-r-none text-[#2C3E50] font-bold":
            pathname.includes(pagePath),
        }),
        className
      )}
    >
      {title}
    </Link>
  );
};

export default SidebarNavItem;
