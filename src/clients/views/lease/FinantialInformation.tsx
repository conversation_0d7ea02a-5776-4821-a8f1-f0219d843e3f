'use client';
import { Lease } from '@/types/lease';
import Input from '@/clients/ui/input';
import CurrencyInput from '@/clients/ui/currency-input';
import Checkbox from '@/clients/ui/checkbox';
import { useCallback, useEffect, useState } from 'react';

type Props = {
  lease: Lease;
};
export type FinantialInformationErrors = {
  commission_percentage: string;
  rent: string;
  processing_fee: string;
  other_fee: string;
  tax: string;
  security_deposit_amount: string;
  total_amount: string;
};

// exclude file and surrounding section
const FinantialInformation = ({ lease }: Props) => {
  const [errors, setErrors] = useState<FinantialInformationErrors>({
    commission_percentage: '',
    rent: '',
    processing_fee: '',
    other_fee: '',
    tax: '',
    security_deposit_amount: '',
    total_amount: '',
  });
  console.log('lease', lease);
  return (
    <div className="w-full md:border p-2 md:p-4 rounded-lg h-min">
      <p className="text-sm font-bold flex gap-4 items-center">
        <span>
          {/*<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12.9334 15.7333H12.0001V12H11.0668M12.0001 8.26664H12.0094M20.4001 12C20.4001 16.6392 16.6393 20.4 12.0001 20.4C7.36091 20.4 3.6001 16.6392 3.6001 12C3.6001 7.36078 7.36091 3.59998 12.0001 3.59998C16.6393 3.59998 20.4001 7.36078 20.4001 12Z" stroke="#EB1648" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>*/}
        </span>
        Finantial Information
      </p>
      <div className="py-2 flex items-center gap-2 w-full"></div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Comission %</p>
        <div className="w-[50%]">
          <Input
            name="commission_percentage"
            className="text-xs p-2 w-full"
            placeholder="15%"
            defaultValue={
              lease?.commission_percentage
                ? lease.commission_percentage * 100
                : ''
            }
            helperText={errors?.commission_percentage ?? ''}
            error={!!errors.commission_percentage}
          />
        </div>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Rent</p>
        <CurrencyInput
          name="rent"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="Rent"
          defaultValue={lease?.rent ?? ''}
          helperText={errors?.rent ?? ''}
          error={!!errors.rent}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Processing Fee</p>
        <CurrencyInput
          name="processing_fee"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="Processing Fee"
          defaultValue={lease?.processing_fee ?? ''}
          helperText={errors?.processing_fee ?? ''}
          error={!!errors?.processing_fee?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Other Fees</p>
        <span className="text-xs p-0 flex-grow border-0 text-right">
          {lease?.other_fee}
        </span>
      </div>
      {lease?.other_fees?.map((fee) => (
        <div
          key={fee.amount}
          className="py-2 flex items-center gap-2 w-full pl-8"
        >
          <p className="w-[30%] text-xs">{fee.reason}</p>
          <div className="w-[30%] text-xs flex items-center gap-2">
            Taxable <Checkbox checked={fee.taxable} />
          </div>
          <CurrencyInput
            name="other_fee"
            wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
            className="text-xs p-0 flex-grow border-0"
            placeholder="Processing Fee"
            value={fee?.amount ?? ''}
            helperText={errors?.other_fee ?? ''}
            error={!!errors?.other_fee?.length}
          />
        </div>
      ))}
      {/*<div className="py-2 flex items-center gap-2 w-full pl-8">
          <Input
              name="min_night_stay"
              className="w-[30%] text-xs"
              placeholder="15%"
              value={lease?.other_fee ?? ""}
              helperText={errors?.other_fee ?? ""}
              error={!!errors?.other_fee?.length}
            />
          <div className="w-[30%] text-xs flex items-center gap-2">Taxable <Checkbox /></div>
          <CurrencyInput
            name="other_fee"
            wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
            className="text-xs p-0 flex-grow border-0"
            placeholder="Processing Fee"
            value={lease?.other_fee ?? ""}
            helperText={errors?.other_fee ?? ""}
            error={!!errors?.other_fee?.length}
        />
        </div>*/}
      <div className="py-2 flex items-center gap-2 w-full pl-8">
        <p className="w-[30%] text-xs">+ Add Other Fee</p>
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[30%] text-xs">Occupancy Tax</p>
        <div className="w-[30%] text-xs flex items-center gap-2">
          Exempt from Occupancy Tax
          <Checkbox name="tax_exempt" checked={lease?.tax_exempt} />
        </div>
        <CurrencyInput
          name="tax"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="tax"
          defaultValue={lease?.tax ?? ''}
          helperText={errors?.tax ?? ''}
          error={!!errors?.tax?.length}
        />
      </div>
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-xs">Security Deposit Tax</p>
        <CurrencyInput
          name="security_deposit_amount"
          wrapperclassName="w-[50%] flex-center gap-1 p-2 border rounded"
          className="text-xs p-0 flex-grow border-0"
          placeholder="Security Deposit"
          defaultValue={lease?.security_deposit_amount ?? ''}
          helperText={errors?.security_deposit_amount ?? ''}
          error={!!errors?.security_deposit_amount?.length}
        />
      </div>
      <hr className="pb-1" />
      <hr />
      <div className="py-2 flex items-center gap-2 w-full">
        <p className="w-[50%] text-md font-bold">Grand Total</p>
        <span className="text-ms font-bold p-0 flex-grow border-0 text-right">
          {lease?.total_amount ?? '0.00'}
        </span>
      </div>
    </div>
  );
};

export default FinantialInformation;
