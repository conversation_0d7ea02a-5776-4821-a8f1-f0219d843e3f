'use client';

import React, { FormEvent, memo, useCallback, useMemo } from 'react';
import { FieldArray, FormikErrors, FormikTouched } from 'formik';
import Input from '@/clients/ui/input';
import Checkbox from '@/clients/ui/checkbox';
import { calculateGrandTotalFinancialInfo } from '@/utils/lease';
import classNames from 'classnames';
import FormHelperText from '@/app/ui/form-helper-text';
import CurrencyInput from '@/clients/ui/currency-input';
import { Lease } from '@/types/lease';

type FinancialFormProps = {
  values: FinancialFormValues;
  setFieldValue: (field: string, value: any) => void;
  errors?: FormikErrors<FinancialFormValues>;
  touched?: FormikTouched<FinancialFormValues>;
  validateField: (field: string) => void;
  lease?: Lease;
};

interface OccupancyTax {
  amount?: number;
  exempt?: boolean;
}

export interface FinancialFormValues {
  commission?: number;
  rent: number;
  processingFee: number;
  otherFees: {
    amount: number;
    reason: string;
    taxable: boolean;
    custom?: boolean;
  }[];
  occupancyTax: OccupancyTax;
  securityDeposit: number;
}

const FinancialForm: React.FC<FinancialFormProps> = ({
  values,
  errors,
  setFieldValue,
  touched,
  validateField,
  lease,
}) => {
  const editAllowed = useMemo(
    () => (lease && lease?.status === 'Draft') || !lease,
    [lease]
  );
  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) {
        return;
      }

      setFieldValue(name, Number(number));
      setTimeout(() => validateField(name), 10);
    },
    [setFieldValue, validateField]
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      setFieldValue(name, value);
      setTimeout(() => validateField(name), 10);
    },
    [setFieldValue, validateField]
  );

  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      setFieldValue(name, checked);
      setTimeout(() => validateField(name), 10);
    },
    [setFieldValue, validateField]
  );

  return (
    <div>
      <div className="flex items-center justify-between my-2">
        <div className="relative min-w-[240px]">
          <label className="text-xs md:text-sm">Commission %</label>
          {errors?.commission && (
            <FormHelperText className="text-[10px] absolute top-4 left-1" error>
              {errors?.commission}
            </FormHelperText>
          )}
        </div>

        <Input
          name="financialInfo.commission"
          className={classNames('w-32 text-xs md:text-sm !p-2 text-right', {
            'border-[#F37391]': errors?.commission,
            'hover:border-black': editAllowed,
          })}
          onChange={onChangeNumeric}
          value={values.commission}
          error={!!errors?.commission}
          disabled={!editAllowed}
        />
      </div>
      <div className="flex items-center justify-between my-2">
        <div className="relative min-w-[240px]">
          <label className="text-xs md:text-sm">Rent</label>
          {errors?.rent && (
            <FormHelperText className="text-[10px] absolute top-4 left-1" error>
              {errors?.rent}
            </FormHelperText>
          )}
        </div>

        <CurrencyInput
          name="financialInfo.rent"
          wrapperclassName={classNames(
            'w-32 flex items-center border rounded-[4px] p-2',
            {
              'border-[#F37391]': errors?.rent,
              'hover:border-black': editAllowed,
            }
          )}
          className="text-xs md:text-sm !p-0 border-none w-full"
          onChange={onChangeNumeric}
          value={values.rent}
          error={!!errors?.rent}
          disabled={!editAllowed}
        />
      </div>

      <div className="flex items-center justify-between my-2">
        <div className="relative min-w-[240px]">
          <label className="text-xs md:text-sm">Processing Fee</label>
          {errors?.processingFee && (
            <FormHelperText className="text-[10px] absolute top-4 left-1" error>
              {errors?.processingFee}
            </FormHelperText>
          )}
        </div>

        <CurrencyInput
          name="financialInfo.processingFee"
          wrapperclassName={classNames(
            'w-32 flex items-center border rounded-[4px] p-2',
            {
              'border-[#F37391]': errors?.processingFee,
              'hover:border-black': editAllowed,
            }
          )}
          className="text-xs md:text-sm !p-0 border-none w-full"
          onChange={onChangeNumeric}
          value={values.processingFee}
          error={!!errors?.processingFee}
          disabled={!editAllowed}
        />
      </div>

      <FieldArray name="financialInfo.otherFees">
        {({ push }) => (
          <>
            <div>
              <label className="text-xs md:text-sm">Other Fees</label>
            </div>
            {values.otherFees.map((fee, index) => (
              <div
                key={index}
                className="w-full flex items-center justify-between pl-4 my-1"
              >
                <div className="flex items-center justify-between gap-x-4">
                  <Input
                    name={`financialInfo.otherFees.${index}.reason`}
                    className="w-32 text-xs md:text-sm !p-2"
                    onChange={onChangeTextInput}
                    value={values.otherFees[index]?.reason ?? ''}
                    error={!!errors?.processingFee}
                    disabled={!editAllowed}
                  />
                  <div className="flex items-center gap-x-1">
                    <p className="text-[10px] md:text-xs">Taxable</p>
                    <Checkbox
                      id={`otherFees.${index}.taxable`}
                      name={`financialInfo.otherFees.${index}.taxable`}
                      checked={values.otherFees[index]?.taxable}
                      onChange={onToggleCheckobox}
                      disabled={!editAllowed}
                    />
                  </div>
                </div>
                <CurrencyInput
                  name={`financialInfo.otherFees.${index}.amount`}
                  wrapperclassName={classNames(
                    'w-20 flex items-center border rounded-[4px] p-2'
                  )}
                  className="text-xs md:text-sm !p-0 border-none w-full"
                  pattern="\d*"
                  inputMode="numeric"
                  onChange={onChangeNumeric}
                  value={values.otherFees[index]?.amount}
                  disabled={!editAllowed}
                />
              </div>
            ))}

            {editAllowed && (
              <button
                type="button"
                disabled={!editAllowed}
                className="pl-2 py-2 inline-block ml-0 mr-auto text-[10px] md:text-xs"
                onClick={() =>
                  push({
                    reason: '',
                    taxable: false,
                    amount: 0,
                    custom: true,
                  })
                }
              >
                + Add Other Fee
              </button>
            )}
          </>
        )}
      </FieldArray>
      <div className="flex justify-between items-center my-2">
        <label htmlFor="occupancyTax" className="text-xs md:text-sm">
          Occupancy Tax
        </label>

        <div className="flex gap-x-2 items-center">
          <label htmlFor="exempt" className="text-[10px] md:text-xs">
            Exempt
          </label>
          <Checkbox
            id="exempt"
            name="financialInfo.occupancyTax.exempt"
            checked={values?.occupancyTax?.exempt}
            onChange={onToggleCheckobox}
            disabled={!editAllowed}
          />
        </div>
        <CurrencyInput
          name="financialInfo.occupancyTax.amount"
          wrapperclassName={classNames(
            'w-32 flex items-center border rounded-[4px] p-2',
            {
              'border-[#F37391]': errors?.occupancyTax?.amount,
              'hover:border-black': editAllowed,
            }
          )}
          className="text-xs md:text-sm !p-0 border-none w-full"
          onChange={onChangeNumeric}
          value={values.occupancyTax.amount}
          error={!!errors?.occupancyTax?.amount}
          disabled={!editAllowed}
        />
      </div>
      <div className="flex justify-between items-center my-2">
        <label htmlFor="securityDeposit" className="text-[10px] md:text-xs">
          Security Deposit
        </label>
        <CurrencyInput
          name="financialInfo.securityDeposit"
          wrapperclassName={classNames(
            'w-32 flex items-center border rounded-[4px] p-2',
            {
              'border-[#F37391]': errors?.securityDeposit,
              'hover:border-black': editAllowed,
            }
          )}
          className="text-xs md:text-sm !p-0 border-none w-full"
          onChange={onChangeNumeric}
          value={values.securityDeposit}
          error={!!errors?.securityDeposit}
          disabled={!editAllowed}
        />
      </div>
      <hr className="my-4" />
      <div className="flex justify-between text-sm md:text-base">
        <h3 className="font-bold">Grand Total</h3>
        <p className="w-32 text-black font-bold text-right">
          ${calculateGrandTotalFinancialInfo(values).toFixed(2)}
        </p>
      </div>
    </div>
  );
};

export default memo(FinancialForm);
