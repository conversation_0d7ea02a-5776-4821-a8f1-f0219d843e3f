'use client';

import { Lease } from '@/types/lease';
import AddressAutocomplete from '../../common/AddressAutocomplete';
import { memo, useCallback, useMemo } from 'react';
import SelectTenantInput from './SelectTenantInput';
import { Contact, UserProfile } from '@/types/profile';
import { ListingAddressPayload, Property } from '@/types/property';
import DateRangePicker from '@/clients/components/common/DateRangePicker';
import { DateRange } from 'react-day-picker';
import DateRangePickerMobile from '@/clients/components/common/DateRangePicker/DateRangePickerMobile';
import { format } from 'date-fns';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import FormHelperText from '@/app/ui/form-helper-text';
import { Nullable } from '@/types/common';
import { FormikErrors, FormikTouched } from 'formik';
import { getFullName } from '@/utils/common';
import dayjs from 'dayjs';
import classNames from 'classnames';

export type LeaseInfoFormValues = {
  listing_address: string;
  tenantName?: string;
  leasingAgent?: string;
  landlordName: string;
  dates?: {
    from: Date;
    to: Date;
  };
};

type Props = {
  listingId: Nullable<number>;
  values: LeaseInfoFormValues;
  errors?: FormikErrors<LeaseInfoFormValues>;
  touched?: FormikTouched<LeaseInfoFormValues>;
  setListingId: (_id: Nullable<number>) => void;
  setFieldValue: (field: string, value: any) => void;
  setTenant: (_t: Nullable<Contact>) => void;
  setAgent: (_a: Nullable<UserProfile>) => void;
  validateField: (field: string) => void;
  lease?: Lease;
  listingDetails?: Nullable<Property>;
  userData: UserProfile;
};

const LeaseInfoForm = ({
  values,
  errors,
  setListingId,
  setFieldValue,
  setTenant,
  setAgent,
  validateField,
  lease,
  listingDetails,
  userData,
}: Props) => {
  const editAllowed = useMemo(
    () => (lease && lease?.status === 'Draft') || !lease,
    [lease]
  );
  const onSelectAddress = useCallback(
    (_a: ListingAddressPayload) => {
      console.log('address is', _a);
      setListingId(_a.listing_id);
      setFieldValue('leaseInfo.listing_address', _a.address);
      setTimeout(() => validateField('leaseInfo.listing_address'), 10);
      setFieldValue(
        'leaseInfo.landlordName',
        getFullName(_a?.owner_first_name ?? '', _a?.owner_last_name ?? '')
      );
    },
    [setFieldValue, setListingId, validateField]
  );

  const onSelectTenant = useCallback(
    (_t?: Contact) => {
      setFieldValue(
        'leaseInfo.tenantName',
        getFullName(_t?.first_name, _t?.last_name)
      );
      setTimeout(() => validateField('leaseInfo.tenantName'), 10);
      setTenant(_t ?? null);
    },
    [setFieldValue, setTenant, validateField]
  );

  const onSelectAgent = useCallback(
    (_a?: UserProfile) => {
      setFieldValue(
        'leaseInfo.leasingAgent',
        getFullName(_a?.first_name, _a?.last_name)
      );
      setTimeout(() => validateField('leaseInfo.leasingAgent'), 10);
      setAgent(_a ?? null);
    },
    [setAgent, setFieldValue, validateField]
  );

  const onDateChange = useCallback(
    (_d?: DateRange) => {
      setFieldValue('leaseInfo.dates', _d);
      setTimeout(() => validateField('leaseInfo.dates'), 10);
    },
    [setFieldValue, validateField]
  );

  return (
    <>
      <div className="p-4 bg-navy rounded-[10px] grid md:grid-cols-2 xl:grid-cols-3 gap-2">
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Address:</span>
          <div className="w-full">
            <AddressAutocomplete
              name="leaseInfo.listing_address"
              value={values.listing_address}
              placeholder="Listing Address"
              className="!text-xs !p-0 font-semibold"
              onSelectAddress={onSelectAddress}
              disabled={!editAllowed}
            />
            {errors?.listing_address && (
              <FormHelperText error className="text-[10px]">
                {errors?.listing_address}
              </FormHelperText>
            )}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Tenant:</span>
          <div className="w-full">
            <SelectTenantInput
              name="leaseInfo.tenantName"
              value={values.tenantName}
              onSelectTenant={onSelectTenant}
              disabled={!editAllowed}
            />
            {errors?.tenantName && (
              <FormHelperText error className="text-[10px]">
                Tenant is required
              </FormHelperText>
            )}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2">
          <span className="md:w-[120px] xl:w-[90px] text-xs">
            Leasing Agent:
          </span>
          <div className="w-full font-medium text-xs">
            {userData?.first_name + ' ' + userData?.last_name}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Landlord:</span>
          <p className="text-xs font-semibold">{values.landlordName}</p>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2">
          <span className="md:w-[120px] xl:w-[90px] text-xs">Dates:</span>
          <div className="hidden md:block w-full">
            <DateRangePicker
              date={values.dates}
              setDate={onDateChange}
              className="w-full p-0"
              rentalRates={listingDetails?.rates ?? []}
              availableCalendar={listingDetails?.availabilities ?? []}
              title={
                <div
                  className={classNames(
                    'flex items-center justify-between w-full text-xs font-semibold'
                  )}
                >
                  <p>
                    {values?.dates?.from
                      ? format(values.dates.from, 'E, LLL d, yyyy')
                      : 'Start date'}
                  </p>
                  <ArrowRightIcon className="w-4 h-4" />
                  <p>
                    {values?.dates?.to
                      ? format(values.dates.to, 'E, LLL d, yyyy')
                      : 'End date'}
                  </p>
                </div>
              }
              disabled={!editAllowed}
            />
            {errors?.dates && (
              <FormHelperText error className="text-[10px]">
                Dates are required
              </FormHelperText>
            )}
          </div>
          <div className="md:hidden w-full">
            <DateRangePickerMobile
              date={values.dates}
              setDate={onDateChange}
              className="w-full p-0"
              title={
                <div className="flex items-center justify-between w-full text-xs font-semibold">
                  <p>
                    {values?.dates?.from
                      ? format(values.dates.from, 'E, LLL d, yyyy')
                      : 'Start date'}
                  </p>
                  <ArrowRightIcon className="w-4 h-4" />
                  <p>
                    {values?.dates?.to
                      ? format(values.dates.to, 'E, LLL d, yyyy')
                      : 'End date'}
                  </p>
                </div>
              }
              disabled={!editAllowed}
              rentalRates={listingDetails?.rates ?? []}
              availableCalendar={listingDetails?.availabilities ?? []}
            />
            {errors?.dates && (
              <FormHelperText error className="text-[10px]">
                Dates are required
              </FormHelperText>
            )}
          </div>
        </div>
        <div className="bg-white rounded-md px-4 py-2 flex items-center gap-x-2">
          <span className="md:w-[120px] xl:w-[90px] text-xs">
            Lease Created:
          </span>
          <p className="text-xs font-semibold">
            {lease ? dayjs(lease.created_at).format('MM/DD/YYYY') : 'N/A'}
          </p>
        </div>
      </div>
    </>
  );
};

export default memo(LeaseInfoForm);
