'use client';

import { Formik, FormikProps } from 'formik';
import { useRouter } from 'next/navigation';
import { FinancialFormValues } from './FinancialForm';
import { ClausesFormValues } from './Clauses';
import { PaymentScheduleValues } from './PaymentSchedule';
import { Lease } from '@/types/lease';
import { ReactNode, useCallback, useMemo, useState } from 'react';
import { LeaseInfoFormValues } from './LeaseInfoForm';
import { getFullName, parseStringPrice } from '@/utils/common';
import LeaseDetailsFormWrapper from './LeaseDetailsFormWrapper';
import { leaseValidationSchema } from './leaseValidation';
import { createLease, updateLease } from '@/app/actions/lease';
import { useLease } from '@/clients/contexts/LeaseContext';
import dayjs from 'dayjs';
import {
  calculateGrandTotalFinancialInfo,
  calculateGrandTotalForPaymentItem,
} from '@/utils/lease';
import toast from 'react-hot-toast';
import { Nullable, ProgressStatus } from '@/types/common';
import { UserProfile } from '@/types/profile';

export type LeaseDetailsFormValues = {
  leaseInfo: LeaseInfoFormValues;
  financialInfo: FinancialFormValues;
  clauses: ClausesFormValues;
  paymentSchedule: PaymentScheduleValues;
};

type Props = {
  userData: UserProfile;
  leaseId?: number;
  lease?: Lease;
};

const FormikWrapper = ({ leaseId, lease, userData }: Props) => {
  const router = useRouter();
  const { agent, tenant, rentInfo, listingDetails } = useLease();
  const [progressStatus, setProgressStatus] =
    useState<Nullable<ProgressStatus>>(null);
  const initialValues = useMemo(
    () => ({
      leaseInfo: {
        listing_address: lease?.listing_address ?? '',
        tenantName: getFullName(
          lease?.tenant_first_name,
          lease?.tenant_last_name
        ),
        leasingAgent: getFullName(
          lease?.user_first_name,
          lease?.user_last_name
        ),
        landlordName: getFullName(
          lease?.owner_first_name,
          lease?.owner_last_name
        ),
        dates:
          lease?.arrival_date && lease?.departure_date
            ? {
                from: new Date(lease.arrival_date),
                to: new Date(lease.departure_date),
              }
            : undefined,
      },
      financialInfo: {
        commission: (lease?.commission_percentage ?? 0.15) * 100,
        rent: parseStringPrice(lease?.rent ?? '0'),
        processingFee: parseStringPrice(lease?.processing_fee ?? '100'),
        otherFees:
          lease?.other_fees?.map((fee) => ({
            reason: fee.reason,
            taxable: fee.taxable,
            amount: parseStringPrice(fee.amount),
          })) ?? [],
        occupancyTax: {
          amount: parseStringPrice(lease?.tax ?? '0'),
          exempt: lease?.tax_exempt ?? false,
        },
        securityDeposit: parseStringPrice(
          lease?.security_deposit_amount ?? '0'
        ),
      },
      clauses: {
        noSmoking: lease?.clauses?.includes('No Smoking') ?? false,
        petsOK: lease?.clauses?.includes('Pets OK') ?? false,
        poolWaiver: lease?.clauses?.includes('Pool Waiver Required') ?? false,
        showClause: lease?.clauses?.includes('Show Clause') ?? false,
        roofWaiver:
          lease?.clauses?.includes('Roof Walk Waiver Required') ?? false,
        comment: lease?.comment ?? '',
      },
      paymentSchedule: {
        payments: lease?.payments?.map((_p) => ({
          due_date: _p.due_date,
          rent: parseStringPrice(_p.bill.rent),
          processing_fee: parseStringPrice(_p.bill.processing_fee),
          occupancy_tax: parseStringPrice(_p.bill.tax),
          security_deposit: parseStringPrice(_p.bill.security_deposit),
          other_fee: parseStringPrice(_p.bill.other_fee),
        })) ?? [
          {
            due_date: '',
            rent: 0,
          },
        ],
      },
    }),
    [lease]
  );

  const onSubmitForm = useCallback(
    async (values: LeaseDetailsFormValues) => {
      if (!listingDetails) {
        toast.error(
          'Missing required information. Please check listing details.'
        );
        return;
      }
      if (!rentInfo && !lease) {
        toast.error('Missing required information. Please check rent details.');
        return;
      }

      setProgressStatus(ProgressStatus.LOADING);
      try {
        const leasePayload = {
          // Required tenant information
          tenant: tenant?.contact_id ?? lease?.tenant, // Tenant ID (required)
          // Required property information
          listing: listingDetails.listing_id,
          listing_address: listingDetails?.address ?? '',
          arrival_date: values.leaseInfo.dates?.from
            ? dayjs(values.leaseInfo.dates.from).format('YYYY-MM-DD')
            : '', // Check-in date (required)
          departure_date: values.leaseInfo.dates?.to
            ? dayjs(values.leaseInfo.dates.to).format('YYYY-MM-DD')
            : '', // Check-out date (required)
          checkin_time: listingDetails.requirement.checkin_time, // Check-in time (required)
          checkout_time: listingDetails.requirement.checkout_time, // Check-out time (required)
          // Required financial information
          rent: values.financialInfo.rent ?? 0, // Base rent amount (required)
          processing_fee: values.financialInfo.processingFee ?? 0, // Processing fee (required)
          tax: values.financialInfo?.occupancyTax?.amount ?? 0, // Calculated tax amount (required)
          security_deposit_amount: values.financialInfo.securityDeposit ?? 0, // Security deposit (required)
          commission_percentage: (values?.financialInfo?.commission ?? 0) / 100, // Convert from percentage to decimal (required)
          // Required payment schedule
          payments: values.paymentSchedule.payments.map((_p, index) => {
            return {
              period: index + 1,
              due_date: _p.due_date,
              total_amount: calculateGrandTotalForPaymentItem(_p).toFixed(2),
              bill: {
                rent: _p?.rent ?? 0,
                processing_fee: _p?.processing_fee ?? 0,
                tax: _p?.occupancy_tax ?? 0,
                security_deposit: _p?.security_deposit ?? 0,
                other_fee: _p?.other_fee ?? 0,
              },
            };
          }), // Array of payment objects with due dates (required)
          // Tax exemption information (conditionally required)
          // Other fees
          other_fees: values.financialInfo.otherFees,
          other_fee: values.financialInfo.otherFees.reduce(
            (sum, fee) => sum + fee.amount,
            0
          ),
          // Additional information
          clauses: Object.entries(values.clauses)
            .filter(
              ([key, value]) =>
                typeof value === 'boolean' && value && key !== 'comment'
            )
            .map(([key]) => {
              // Convert camelCase to Title Case with spaces
              return key
                .replace(/([A-Z])/g, ' $1')
                .replace(/^./, (str) => str.toUpperCase())
                .trim();
            }),
          comment: values.clauses.comment,
          // Agent information (required for admins)
          user: agent?.user_id ?? lease?.user,
          // Total amount (calculated)
          total_amount:
            (lease
              ? lease.total_amount
              : calculateGrandTotalFinancialInfo(values.financialInfo)) ?? 0,
        };
        if (leaseId) {
          //update lease
          const data = await updateLease<Lease>(leaseId, leasePayload);
          if (data) {
            setProgressStatus(ProgressStatus.SUCCESSFUL);
            toast.success('Lease updated successfully!');
          } else {
            throw new Error('Failed to update lease.');
          }
        } else {
          //create lease

          const data = await createLease<Lease>(leasePayload);

          if (data) {
            setProgressStatus(ProgressStatus.SUCCESSFUL);
            toast.success('Lease created successfully!');
            router.push(`/lease/${data.lease_id}`);
          } else {
            throw new Error('Failed to create lease.');
          }
        }
      } catch (error) {
        console.error('Error creating lease:', error);
        setProgressStatus(ProgressStatus.FAILED);
        toast.error(
          error instanceof Error
            ? error.message
            : 'Failed to create lease. Please try again.'
        );
      }
    },
    [
      agent?.user_id,
      lease,
      leaseId,
      listingDetails,
      rentInfo,
      router,
      tenant?.contact_id,
    ]
  );

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={leaseValidationSchema}
      validateOnChange={false}
      validateOnBlur={false}
      onSubmit={(values: LeaseDetailsFormValues) => {
        onSubmitForm(values);
      }}
    >
      {({ ...formikProps }: FormikProps<LeaseDetailsFormValues>) => {
        return (
          <LeaseDetailsFormWrapper
            lease={lease}
            leaseId={leaseId}
            progressStatus={progressStatus}
            userData={userData}
            {...formikProps}
          />
        );
      }}
    </Formik>
  );
};

export default FormikWrapper;
