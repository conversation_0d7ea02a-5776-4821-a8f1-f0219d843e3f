import React, { memo, useCallback } from 'react';

import Textarea from '@/clients/ui/textarea';
import Checkbox from '@/clients/ui/checkbox';
import { Lease } from '@/types/lease';

const noSmoking = 'No Smoking - No Smoking is allowed on the premises.';
const petsOK =
  'Pets Allowed - Notwithstanding the foregoing, Landlord agrees to allow tenant to have one dog on the property during tenancy with the understanding that tenant shall be solely responsible for any dog-related damage at the end of lease term. Amount of dog related damage shall not be limited to the amount of the security deposit. Tenant agrees to abide by the Nantucket Leash and Pick Up Laws.';
const poolWaiver =
  'Pool Waiver- Tenant is required to sign the attached Pool/Spa waiver.';
const showClause =
  'Show Clause- Tenant agrees to allow property to be shown to prospective buyers with 24 hour notice from listing broker.';
const roofWaiver =
  'Roofwalk Waiver- Tenant is required to sign the attached Roofwalk Waiver.';

export interface ClausesFormValues {
  noSmoking?: boolean;
  petsOK?: boolean;
  poolWaiver?: boolean;
  showClause?: boolean;
  roofWaiver?: boolean;
  comment: string;
}

interface ClausesFormProps {
  values: ClausesFormValues;
  errors: any;
  setFieldValue: (field: string, value: any) => void;
  validateField: (field: string) => void;
  lease?: Lease;
}

type FieldBlockProps = {
  label: string;
  name: string;
  value: string;
  as: any;
};

const ClausesForm: React.FC<ClausesFormProps> = ({
  values,
  errors,
  setFieldValue,
  validateField,
  lease,
}) => {
  const onToggleCheckobox = useCallback(
    (event: any) => {
      const { name, checked } = event.target;
      setFieldValue(name, checked);
      setTimeout(() => validateField(name), 10);

      let comment = '';
      const no_smoking_show =
        values?.noSmoking || (name === 'clauses.noSmoking' && checked);
      comment += no_smoking_show ? noSmoking + '\r\n\r\n' : '';

      const pets_show =
        values?.petsOK || (name === 'clauses.petsOK' && checked);
      comment += pets_show ? petsOK + '\r\n\r\n' : '';

      const pool_waiver_show =
        values?.poolWaiver || (name === 'clauses.poolWaiver' && checked);
      comment += pool_waiver_show ? poolWaiver + '\r\n\r\n' : '';

      const show_clause_show =
        values?.showClause || (name === 'clauses.showClause' && checked);
      comment += show_clause_show ? showClause + '\r\n\r\n' : '';

      const roofwalk_waiver_show =
        values?.roofWaiver || (name === 'clauses.roofWaiver' && checked);

      comment += roofwalk_waiver_show ? roofWaiver + '\r\n\r\n' : '';

      console.log('comment should be', comment, name, checked);

      setFieldValue('clauses.comment', comment);
    },
    [setFieldValue, validateField, values]
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      setFieldValue(name, value);
      setTimeout(() => validateField(name), 10);
    },
    [setFieldValue, validateField]
  );

  return (
    <div className="flex flex-col gap-y-2.5">
      <div className="flex flex-col gap-y-2.5">
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="noSmoking" className="text-xs md:text-sm">
            No Smoking
          </label>
          <Checkbox
            id="noSmoking"
            name="clauses.noSmoking"
            checked={values.noSmoking}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="petsOK" className="text-xs md:text-sm">
            Pets Allowed
          </label>
          <Checkbox
            id="petsOK"
            name="clauses.petsOK"
            checked={values.petsOK}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="poolWaiver" className="text-xs md:text-sm">
            Pool Waiver Required
          </label>
          <Checkbox
            id="poolWaiver"
            name="clauses.poolWaiver"
            checked={values.poolWaiver}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="showClause" className="text-xs md:text-sm">
            Show Clause
          </label>
          <Checkbox
            id="showClause"
            name="clauses.showClause"
            checked={values.showClause}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
        <div className="flex justify-between items-center md:w-5/6">
          <label htmlFor="roofWaiver" className="text-xs md:text-sm">
            Roof Walker Waiver Required
          </label>
          <Checkbox
            id="roofWaiver"
            name="clauses.roofWaiver"
            checked={values.roofWaiver}
            onChange={onToggleCheckobox}
            disabled={lease && lease.status !== 'Draft'}
          />
        </div>
      </div>

      <div className="md:mt-8">
        <div>
          <label htmlFor="comment" className="text-xs md:text-sm font-bold">
            Signature Page Additions
          </label>
          <p className="text-[10px] italic">
            *This text will be printed on the signature page.
          </p>
        </div>
        <Textarea
          id="comment"
          name="clauses.comment"
          value={values.comment}
          onChange={onChangeTextInput}
          className="w-full h-24 text-[10px] text-xs"
          placeholder="Enter text here"
          disabled={lease && lease.status !== 'Draft'}
        />
      </div>
    </div>
  );
};

export default memo(ClausesForm);
