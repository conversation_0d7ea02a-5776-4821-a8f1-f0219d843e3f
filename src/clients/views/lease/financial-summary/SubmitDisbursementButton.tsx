'use client';

import { DisbursementInfo } from '@/types/lease';
import { memo, useCallback, useState } from 'react';
import dayjs from 'dayjs';
import Button from '@/clients/ui/button';
import { submitDisbursement } from '@/app/actions/lease';
import { ProgressStatus } from '@/types/common';
import toast from 'react-hot-toast';
import { revalidateTagByName } from '@/app/actions/revalidateTag';

type Props = {
  disbursementForm: DisbursementInfo;
  leaseId: number;
  disabled?: boolean;
};

const SubmitDisbursementButton = ({
  disbursementForm,
  leaseId,
  disabled,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const onSubmit = useCallback(() => {
    setProgressStatus(ProgressStatus.LOADING);
    submitDisbursement(disbursementForm.disbursement_form_uuid, {
      ...disbursementForm,
    })
      .then((res: any) => {
        console.log('status', res);
        const { status, data } = res;
        if (status >= 200 && status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Adjustment recorded successfully!');
        } else {
          console.log('data is', data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(data.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [disbursementForm, leaseId]);

  return (
    <Button
      disabled={disabled}
      onClick={onSubmit}
      isLoading={progressStatus === ProgressStatus.LOADING}
      className="rounded-md bg-success hover:bg-success/70 text-xs min-h-[26px] flex justify-self-end min-w-[220px]"
    >
      {disbursementForm.submit_at
        ? `Submitted by Agent on ${dayjs(disbursementForm.submit_at).format(
            'MM/DD/YYYY'
          )}`
        : `Submit Return Authorization`}
    </Button>
  );
};

export default memo(SubmitDisbursementButton);
