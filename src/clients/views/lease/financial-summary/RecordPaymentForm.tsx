'use client';

import { recordTransaction } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import Input from '@/clients/ui/input';
import useForm from '@/hooks/useForm';
import { ProgressStatus } from '@/types/common';
import dayjs from 'dayjs';
import { memo, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  payment_uuid: string;
  isCompleted?: boolean;
  leaseId: number;
  balance: number;
};

type FormValues = {
  amount: string;
  reference: string;
};

const RecordPaymentForm = ({
  payment_uuid,
  isCompleted,
  leaseId,
  balance,
}: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const { formState, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      amount: '',
      reference: '',
    },
    {
      amount: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Amount is required.`;
        } else if (isNaN(Number(_value))) {
          return `Amount must be a number.`;
        } else if (Number(_value) < 0) {
          return `Amount must be greater than zero.`;
        }
      },
      reference: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Reference is required.`;
        }
      },
    }
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange]
  );

  const onSubmit = useCallback(() => {
    console.log('form', formState);
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      Object.values(_errors).map(
        (_error) => _error !== '' && toast.error(_error ?? '')
      );
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      payment: payment_uuid,
      payment_date: dayjs().format('YYYY-MM-DD'),
      amount: formState.amount,
      reference: formState.reference,
    };

    recordTransaction(payment_uuid, payload)
      .then(({ data, status }: any) => {
        if (status >= 200 && status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Payment recorded successfully!');
        } else {
          console.log('data is', data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(data.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, leaseId, payment_uuid, preSubmitCheck]);

  return (
    <div className="flex gap-x-4 items-center mb-2 w-full">
      <Button
        onClick={onSubmit}
        disabled={isCompleted}
        isLoading={progressStatus === ProgressStatus.LOADING}
        className="text-xs w-[46%] min-h-[26px] rounded-md bg-success hover:bg-success/70"
      >
        Record Payment
      </Button>
      <Input
        value={formState.reference}
        onChange={onChangeTextInput}
        error={!!errors?.reference?.length}
        disabled={isCompleted}
        name="reference"
        placeholder="Ref."
        wrapperclassName="w-[24%]"
        className="!text-xs !px-4 !py-2 w-full"
      />
      <Input
        value={formState.amount}
        onChange={onChangeTextInput}
        error={!!errors?.amount?.length}
        disabled={isCompleted}
        name="amount"
        placeholder="$ Amt."
        wrapperclassName="w-[24%]"
        className="!text-xs !px-4 !py-2 w-full"
      />
    </div>
  );
};

export default memo(RecordPaymentForm);
