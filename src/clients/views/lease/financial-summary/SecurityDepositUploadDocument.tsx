'use client';

import { uploadFile, uploadSecurityDepositFiles } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import { Nullable, ProgressStatus } from '@/types/common';
import { ChangeEvent, memo, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
};

const SecurityDepositUploadDocument = ({ leaseId }: Props) => {
  const [progressStatus, setProgressStatus] =
    useState<Nullable<ProgressStatus>>(null);
  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setProgressStatus(ProgressStatus.LOADING);
      const file = event.target.files[0];
      console.log('file is', file);

      try {
        let data = new FormData();
        data.append('file', file);
        data.append('file_name', file.name);
        uploadFile(data)
          .then(({ data, status }) => {
            if (status >= 200 && status < 300) {
              uploadSecurityDepositFiles(leaseId, {
                attach_file: data.object_uuid,
              })
                .then((data) => {
                  console.log('response', data);
                  revalidateTagByName(`lease-details-${leaseId}`);
                  setProgressStatus(ProgressStatus.SUCCESSFUL);
                  toast.success('File uploaded successfully!');
                })
                .catch((err) => {
                  console.log('response', err);
                  setProgressStatus(ProgressStatus.FAILED);
                  toast.error('Something went wrong. Please try again later.');
                });
            } else {
              setProgressStatus(ProgressStatus.FAILED);
              toast.error('Something went wrong. Please try again later.');
            }
          })
          .catch((err) => {
            console.log('response', err);
          });
      } catch {}
    }
  };

  return (
    <div className="relative flex items-center justify-end w-full">
      <Button
        className="!bg-[#2C3E50] hover:bg-[#2C3E50]/70 rounded-md mt-2 text-sm !font-normal w-[220px]"
        isLoading={progressStatus === ProgressStatus.LOADING}
      >
        Upload Document
      </Button>
      <input
        id="signed-lease"
        className="absolute right-0 top-0 bottom-0 opacity-0 z-10 w-[220px] cursor-pointer"
        type="file"
        accept="application/pdf"
        onChange={handleFileChange}
        disabled={progressStatus === ProgressStatus.LOADING}
      />
    </div>
  );
};

export default memo(SecurityDepositUploadDocument);
