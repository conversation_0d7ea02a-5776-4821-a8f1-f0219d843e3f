'use client';

import { returnSecurityDeposit } from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/button';
import CurrencyInput from '@/clients/ui/currency-input';
import Modal from '@/clients/ui/modal';
import useForm from '@/hooks/useForm';
import { Nullable, ProgressStatus } from '@/types/common';
import { SecurityDeposit } from '@/types/lease';
import { FormEvent, memo, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  leaseId: number;
  disabled?: boolean;
  securityDeposit: Nullable<SecurityDeposit>;
};

type FormValues = {
  homeowner: string;
  tenant: string;
};

const SecurityDepositReturnButton = ({
  leaseId,
  disabled,
  securityDeposit,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const { formState, errors, onChange, preSubmitCheck } = useForm<FormValues>(
    {
      homeowner: '',
      tenant: '',
    },
    {
      homeowner: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Homeowner amount is required.`;
        } else if (isNaN(Number(_value))) {
          return `Homeowner amount must be a number.`;
        } else if (Number(_value) < 0) {
          return `Homeowner amount must be greater than zero.`;
        }
      },
      tenant: (_v, _n, _value) => {
        if (_value.trim().length === 0) {
          return `Tenant amount is required.`;
        } else if (isNaN(Number(_value))) {
          return `Tenant amount must be a number.`;
        } else if (Number(_value) < 0) {
          return `Tenant amount must be greater than zero.`;
        }
      },
    }
  );

  const onChangeNumeric = useCallback(
    (e: FormEvent<HTMLInputElement>) => {
      const { name, value } = e.currentTarget;
      const number = value.replace(/,/g, '');
      if (isNaN(Number(number))) {
        return;
      }
      onChange(number, name);
    },
    [onChange]
  );

  const onSubmit = useCallback(() => {
    console.log('form', formState);
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      Object.values(_errors).map(
        (_error) => _error !== '' && toast.error(_error ?? '')
      );
      return;
    }

    if (
      securityDeposit?.amount &&
      Number(formState.homeowner) + Number(formState.tenant) >
        Number(securityDeposit?.amount ?? 0)
    ) {
      toast.error('Amount cannot be greater than the security deposit');
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);
    const payload = {
      lease: leaseId,
      tenant: formState.tenant ?? '',
      homeowner: formState.homeowner ?? '',
    };

    returnSecurityDeposit(leaseId, { return_info: payload })
      .then(({ data, status }: any) => {
        console.log({ data, status });
        if (status >= 200 && status < 300) {
          revalidateTagByName(`lease-details-${leaseId}`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          toast.success('Returned successfully!');
        } else {
          console.log('data is', data);
          setProgressStatus(ProgressStatus.FAILED);
          toast.error(data.detail);
        }
      })
      .catch((err) => {
        console.log('error is', err);
        toast.error(err.message);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState, leaseId, preSubmitCheck, securityDeposit?.amount]);

  return (
    <>
      <Button
        disabled={disabled}
        onClick={() => setOpen((_o) => !_o)}
        isLoading={progressStatus === ProgressStatus.LOADING}
        className="rounded-md bg-success hover:bg-success/70 text-xs min-h-[26px] flex justify-self-end min-w-[220px] mt-2"
      >
        Submit Return Authorization
      </Button>
      {open && (
        <Modal open className="p-4">
          <p className="text-lg font-semibold my-2">Refund of Deposit</p>
          <hr />
          <CurrencyInput
            value={formState.homeowner}
            onChange={onChangeNumeric}
            error={!!errors?.homeowner?.length}
            disabled={disabled}
            name="homeowner"
            placeholder="Refund amount to homeowner"
            wrapperclassName="w-full my-4 flex-center gap-1 p-2 border rounded"
            className="!text-xs !px-4 !py-2 w-full border-transparent hover:border-transparent text-left"
          />
          <CurrencyInput
            value={formState.tenant}
            onChange={onChangeNumeric}
            error={!!errors?.tenant?.length}
            disabled={disabled}
            name="tenant"
            placeholder="Refund amount to tenant"
            wrapperclassName="w-full my-4 flex-center gap-1 p-2 border rounded"
            className="!text-xs !px-4 !py-2 w-full border-transparent hover:border-transparent text-left"
          />
          <div className="flex items-center justify-between">
            <Button intent="outline" onClick={() => setOpen(false)}>
              Close
            </Button>
            <Button
              disabled={progressStatus === ProgressStatus.LOADING}
              isLoading={progressStatus === ProgressStatus.LOADING}
              onClick={onSubmit}
              className="!font-semibold min-w-[140px]"
            >
              Submit
            </Button>
          </div>
        </Modal>
      )}
    </>
  );
};

export default memo(SecurityDepositReturnButton);
