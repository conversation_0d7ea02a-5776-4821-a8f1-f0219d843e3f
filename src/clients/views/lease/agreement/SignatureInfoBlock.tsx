'use client';

import dayjs from 'dayjs';
import { memo, useCallback, useMemo, useState } from 'react';
import cn from 'classnames';
import toast from 'react-hot-toast';

import { sendSignRequest } from '@/app/actions/lease';
import { Nullable } from '@/types/common';
import { Lease } from '@/types/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import {
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface SignatureInfoBlockProps {
  type: 'Homeowner' | 'Tenant' | '';
  uploadedAt?: Nullable<string>;
  signedAt?: Nullable<number>;
  sent?: number[];
  lease: Lease;
}

type SendStatus = {
  status: 'idle' | 'loading' | 'success' | 'error';
  message?: string;
};

const SignatureInfoBlock = ({
  type = '',
  signedAt,
  sent,
  uploadedAt,
  lease,
}: SignatureInfoBlockProps) => {
  const [sendStatus, setSendStatus] = useState<SendStatus>({
    status: 'idle',
  });
  const hasSigned = useMemo(
    () => !!(signedAt && typeof signedAt === 'number'),
    [signedAt]
  );
  const dateString = useMemo(
    () => (signedAt ? dayjs(signedAt * 1000).format('DD/MM/YYYY h:m A') : null),
    [signedAt]
  );

  const handleSendForSignature = useCallback(async () => {
    setSendStatus({ status: 'loading' });

    try {
      const data = await sendSignRequest<string | { detail: string }>({
        lease: lease.lease_id,
        to: type?.toLowerCase() as any,
      });
      console.log('data is', data);
      if (data === 'succeed') {
        revalidateTagByName(`lease-details-${lease.lease_id}`);

        setSendStatus({
          status: 'success',
          message: `${type} notified.`,
        });
        toast.success(`${type} notified.`);
      } else {
        toast.error((data as any).detail);
        setSendStatus({
          status: 'error',
          message: (data as any).detail,
        });
      }
    } catch (error) {
      console.log('error is', error);
      setSendStatus({
        status: 'error',
        message: `Something went wrong. Please try again later.`,
      });
    }
  }, [lease.lease_id, type]);

  return (
    <div className="p-4 border border-solid border-outline rounded-md w-full md:text-lg">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-bold">{type} Signature</h3>
        {hasSigned ? (
          <CheckCircleIcon className="w-5 h-5 stroke-2 text-green-500" />
        ) : (
          <InformationCircleIcon className="w-5 h-5 stroke-2 text-red-500" />
        )}
      </div>

      <div
        className={cn('text-center rounded-md px-4 py-3 text-xs mt-4 mb-2', {
          'bg-[#D6EEDD] text-[#34A853]': hasSigned,
          'bg-[#FBD0DA] text-[#EB1648]': !hasSigned,
        })}
      >
        {hasSigned ? (
          <div>
            <b>Signed</b>
            {' - '}
            {signedAt && dayjs(signedAt * 1000).format('DD/MM/YYYY')}
          </div>
        ) : (
          <b>Signature missing</b>
        )}
      </div>

      {sent?.map((timestamp, index) => {
        const formatted = dayjs(timestamp * 1000).format('DD/MM/YYYY h:m A');

        return (
          <div key={index} className="text-xs px-4 py-1">
            <b>Sent</b> on {formatted}
          </div>
        );
      })}

      {hasSigned ? (
        <div className="text-xs px-4 py-1">
          <b>Signed</b> on {dateString}
        </div>
      ) : (
        <button
          className={cn(
            'bg-[#2C3E50] text-white rounded-md mt-2 py-2 p-4 flex items-center justify-center gap-x-2 w-full text-xs',
            { 'opacity-50': sendStatus.status === 'loading' }
          )}
          disabled={sendStatus.status === 'loading'}
          onClick={handleSendForSignature}
        >
          <svg
            width="16"
            height="13"
            viewBox="0 0 16 13"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.64286 6.1579L1.92857 2.73684V9.57895H8.35714V10.9474H1.92857C1.53571 10.9474 1.1994 10.8134 0.919643 10.5454C0.639881 10.2774 0.5 9.95526 0.5 9.57895V1.36842C0.5 0.992105 0.639881 0.669956 0.919643 0.401974C1.1994 0.133991 1.53571 0 1.92857 0H13.3571C13.75 0 14.0863 0.133991 14.3661 0.401974C14.6458 0.669956 14.7857 0.992105 14.7857 1.36842V6.1579H13.3571V2.73684L7.64286 6.1579ZM7.64286 4.78947L13.3571 1.36842H1.92857L7.64286 4.78947ZM12.6429 13L11.6429 12.0421L12.7679 10.9474H9.78572V9.57895H12.7679L11.625 8.48421L12.6429 7.52632L15.5 10.2632L12.6429 13ZM1.92857 2.73684V10.2632V6.1579V6.20921V1.36842V2.73684Z"
              fill="white"
            />
          </svg>

          {sendStatus.status === 'loading'
            ? 'Sending...'
            : `Send for ${type} Signature`}
        </button>
      )}
    </div>
  );
};

export default memo(SignatureInfoBlock);
