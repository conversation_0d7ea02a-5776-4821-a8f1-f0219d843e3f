'use client';

import { ChangeEvent, useState } from 'react';
import classNames from 'classnames';
import toast from 'react-hot-toast';

import { uploadSignedLease } from '@/app/actions/lease';

interface LeaseUploadProps {
  leaseId: string;
}

type UploadStatus = {
  status: 'idle' | 'success' | 'error' | 'loading';
  message?: string;
};

export const LeaseUpload = ({ leaseId }: LeaseUploadProps) => {
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>({
    status: 'idle',
  });

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setUploadStatus({ status: 'loading' });
      const file = event.target.files[0];

      if (file.type !== 'application/pdf') {
        setUploadStatus({
          status: 'error',
        });
        toast.error('Invalid file format. Please upload a PDF file instead.');
        return;
      }

      try {
        let data = new FormData();
        data.append('lease', leaseId);
        data.append('file', file);

        await uploadSignedLease(data);

        setUploadStatus({
          status: 'success',
        });
        toast.success('File uploaded successfully');
      } catch {
        setUploadStatus({
          status: 'error',
        });
        toast.error('Something went wrong. Please try again later.');
      }
    }
  };

  return (
    <div className="p-4 border border-solid border-outline rounded-md w-full md:text-lg">
      <button className="bg-[#2C3E50] text-white rounded-md flex items-center justify-center gap-x-2 w-full text-sm cursor-pointer">
        <label
          className={classNames('py-2 p-4 cursor-pointer', {
            'opacity-50': uploadStatus.status === 'loading',
          })}
          htmlFor="signed-lease"
        >
          {uploadStatus.status === 'loading'
            ? 'Uploading file...'
            : 'Upload signed lease'}
        </label>
      </button>
      <input
        id="signed-lease"
        type="file"
        onChange={handleFileChange}
        disabled={uploadStatus.status === 'loading'}
        hidden
      />
    </div>
  );
};
