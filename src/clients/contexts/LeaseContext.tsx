'use client';

import { getPropertyDetails } from '@/app/actions/property';
import { Nullable } from '@/types/common';
import { Lease } from '@/types/lease';
import { Contact, UserProfile } from '@/types/profile';
import { Property, Rent } from '@/types/property';
import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState,
} from 'react';

type LeaseContext = {
  listingId: Nullable<number>;
  setListingId: (val: Nullable<number>) => void;
  listingDetails: Nullable<Property>;
  tenant: Nullable<Contact>;
  setTenant: (val: Nullable<Contact>) => void;
  agent: Nullable<UserProfile>;
  setAgent: (val: Nullable<UserProfile>) => void;
  rentInfo: Nullable<Rent>;
  setRentInfo: (val: Nullable<Rent>) => void;
};

export const LeaseContext = createContext<LeaseContext>({} as LeaseContext);

type ContextProps = {
  children: ReactNode;
  lease?: Lease;
};

export const LeaseContextContainer = ({ children, lease }: ContextProps) => {
  const [listingId, setListingId] = useState<Nullable<number>>(
    lease?.listing ?? null
  );
  const [listingDetails, setListingDetails] =
    useState<Nullable<Property>>(null);
  const [tenant, setTenant] = useState<Nullable<Contact>>(null);
  const [agent, setAgent] = useState<Nullable<UserProfile>>(null);
  const [rentInfo, setRentInfo] = useState<Nullable<Rent>>(null);

  useEffect(() => {
    if (listingId) {
      getPropertyDetails<Property>(listingId)
        .then((data) => {
          setListingDetails(data ?? null);
        })
        .catch((e) => console.log('Failed to fetch listing details', e));
    }
  }, [listingId]);

  return (
    <LeaseContext.Provider
      value={{
        listingId,
        setListingId,
        listingDetails,
        tenant,
        setAgent,
        agent,
        setTenant,
        rentInfo,
        setRentInfo,
      }}
    >
      {children}
    </LeaseContext.Provider>
  );
};

/**
 * Custom hook to access the LeaseContext
 * @returns LeaseContext values and methods
 */
export const useLease = () => {
  const context = useContext(LeaseContext);

  if (!context) {
    throw new Error('useLease must be used within a LeaseContextContainer');
  }

  return context;
};
