'use client';

import { memo, useCallback, useMemo } from 'react';

import { DateRange } from 'react-day-picker';

import classNames from 'classnames';
import {
  format,
  isAfter,
  isBefore,
  isSameDay,
  isValid,
  startOfDay,
} from 'date-fns';
import { PropertyAvailability, PropertyRentalRates } from '@/types/calendar';
import { getBlockedRangesForDate } from '@/utils/date-range-picker';
import { parseDateString } from '@/utils/common';

type Props = {
  dateObject: Date;
  date: number;
  active?: boolean;
  insideCurrentMonth: boolean;
  selected?: DateRange;
  onSelectDate: (_d?: DateRange) => void;
  rentalRates?: PropertyRentalRates[];
  availableCalendar?: PropertyAvailability[];
  blockedStartDates: Date[];
  blockedEndDates: Date[];
};

const MobileDateItem = ({
  date,
  insideCurrentMonth,
  selected,
  dateObject,
  onSelectDate,
  rentalRates = [],
  availableCalendar = [],
  blockedStartDates,
  blockedEndDates,
}: Props) => {
  const isBeforeToday = useMemo(
    () => isBefore(startOfDay(dateObject), startOfDay(new Date())),
    [dateObject]
  );
  const isRangeValid = useMemo(
    () =>
      !!(
        selected?.from &&
        selected?.to &&
        isValid(selected.from) &&
        isValid(selected.to)
      ),
    [selected?.from, selected?.to]
  );

  const blockedRanges = useMemo(
    () => getBlockedRangesForDate(availableCalendar, dateObject),
    [availableCalendar, dateObject]
  );

  const isBlockStartDate = useMemo(
    () => blockedStartDates.some((_d) => isSameDay(_d, dateObject)),
    [blockedStartDates, dateObject]
  );

  const isBlockEndDate = useMemo(
    () => blockedEndDates.some((_d) => isSameDay(_d, dateObject)),
    [blockedEndDates, dateObject]
  );

  const isDisabled = useMemo(() => {
    const blockedRanges = getBlockedRangesForDate(
      availableCalendar,
      dateObject
    );
    const rentalRate = rentalRates.find((_r) =>
      isSameDay(parseDateString(_r.from_date), dateObject)
    );
    return (
      (blockedRanges.length > 0 &&
        !blockedStartDates.some((_d) =>
          isSameDay(startOfDay(dateObject), startOfDay(_d))
        ) &&
        !blockedEndDates.some((_d) =>
          isSameDay(startOfDay(dateObject), startOfDay(_d))
        )) ||
      !rentalRate
    );
  }, [
    availableCalendar,
    blockedEndDates,
    blockedStartDates,
    dateObject,
    rentalRates,
  ]);

  const isSelected = useMemo(
    () =>
      ((selected?.from && isSameDay(dateObject, selected?.from)) ||
        (selected?.to && isSameDay(dateObject, selected?.to))) &&
      insideCurrentMonth,
    [dateObject, insideCurrentMonth, selected?.from, selected?.to]
  );

  const isHighlighted = useMemo(() => {
    return (
      selected?.from &&
      isValid(selected.from) &&
      isAfter(dateObject, selected.from) &&
      selected?.to &&
      isValid(selected.to) &&
      isBefore(dateObject, selected.to) &&
      insideCurrentMonth
    );
  }, [dateObject, insideCurrentMonth, selected]);

  const isStart = useMemo(
    () => selected?.from && isSameDay(dateObject, selected.from),
    [dateObject, selected]
  );
  const isEnd = useMemo(
    () => selected?.to && isSameDay(dateObject, selected.to),
    [dateObject, selected]
  );

  const onClick = useCallback(() => {
    if (isBeforeToday || isDisabled) {
      return;
    }
    if (selected?.from && isSameDay(dateObject, selected.from)) {
      onSelectDate(undefined);
      return;
    }

    if (!selected || isRangeValid) {
      onSelectDate({
        from: dateObject,
        to: undefined,
      });
      return;
    }

    if (selected?.from && isAfter(dateObject, selected.from)) {
      onSelectDate({
        from: selected.from,
        to: dateObject,
      });
    } else {
      onSelectDate({
        from: dateObject,
        to: undefined,
      });
      return;
    }
  }, [
    dateObject,
    isBeforeToday,
    isDisabled,
    isRangeValid,
    onSelectDate,
    selected,
  ]);

  return (
    <td
      id={`date__item-${format(dateObject, 'yyyy-dd-MM')}`}
      className="relative z-1 group "
      onClick={onClick}
    >
      {insideCurrentMonth && (
        <>
          <div
            className={classNames(
              'text-xs flex flex-col items-center justify-center w-full h-ful rounded-full aspect-square font-medium',
              {
                '!bg-carolina-blue': isSelected,
                'bg-carolina-blue-20': isHighlighted,
                'text-[#6D7380]': isBeforeToday,
                'text-white': isStart || isEnd,
              }
            )}
          >
            <p
              className={classNames('z-[2]', {
                'text-outline line-through': isDisabled,
              })}
            >
              {date}
            </p>
          </div>
        </>
      )}
    </td>
  );
};

export default memo(MobileDateItem);
