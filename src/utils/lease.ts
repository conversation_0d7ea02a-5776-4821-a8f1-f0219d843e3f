import { FinancialFormValues } from '@/clients/views/lease/lease-details/FinancialForm';
import { Payment } from '@/clients/views/lease/lease-details/PaymentSchedule';
import { OtherFees } from '@/types/lease';
import dayjs from 'dayjs';

const STATE_TAX = 0.057;
const LOCAL_TAX = 0.06;
const COMMUNITY_IMPACT_TAX = 0.03;

export const calculateOccupancyTax = (
  rent: number,
  processing_fee: number,
  otherFees: OtherFees[] = [],
  tax_exempt?: boolean,
  charge_community_impact_fee?: boolean
): number => {
  if (tax_exempt) return 0;

  const baseAmount = rent + processing_fee;
  let stateTax = baseAmount * STATE_TAX;
  let localTax = baseAmount * LOCAL_TAX;

  if (charge_community_impact_fee) {
    stateTax += baseAmount * COMMUNITY_IMPACT_TAX;
  }

  otherFees.forEach((fee) => {
    if (!fee.taxable || !fee.amount) return;

    const amount = Number(fee.amount);

    stateTax += amount * STATE_TAX;
    if (charge_community_impact_fee) {
      stateTax += amount * COMMUNITY_IMPACT_TAX;
    }

    localTax += amount * LOCAL_TAX;
  });

  return Math.round((stateTax + localTax) * 100) / 100;
};

export const calculateGrandTotalFinancialInfo = (
  values: FinancialFormValues
): number => {
  const rent = Number(values?.rent ?? 0);
  const processingFee = Number(values?.processingFee ?? 0);

  let otherFees = 0;
  if (Array.isArray(values.otherFees)) {
    otherFees = values.otherFees.reduce(
      (sum: number, fee: any) => sum + Number(fee.amount ?? 0),
      0
    );
  }

  const occupancyTax = values?.occupancyTax?.exempt
    ? 0
    : Number(values.occupancyTax?.amount ?? 0);

  const securityDeposit = Number(values?.securityDeposit ?? 0);
  return rent + securityDeposit + processingFee + otherFees + occupancyTax;
};

export const generatePaymentsForLease = (
  totalPayments: number,
  dates: { from: Date; to: Date },
  rent: number,
  financialInfo: FinancialFormValues,
  isDefault?: boolean
) => {
  // Calculate due dates
  const now = dayjs();
  const firstPaymentDate = now.add(7, 'day').format('YYYY-MM-DD'); // 1 week from now

  const lastPaymentDate = dayjs(dates.from)
    .subtract(45, 'day')
    .format('YYYY-MM-DD');

  const isDayOfArrivalMoreThan45Days =
    dayjs(dates.from).diff(dayjs(), 'day') >= 45;

  const newPayments: Payment[] = [];
  const paymentAmount = Math.round((rent / totalPayments) * 100) / 100;

  const shouldDefaultTwoMaxPayments =
    isDefault && isDayOfArrivalMoreThan45Days && totalPayments === 1;

  for (let i = 0; i < (shouldDefaultTwoMaxPayments ? 2 : totalPayments); i++) {
    if (i === 0) {
      newPayments.push({
        due_date: firstPaymentDate,
        rent: paymentAmount,
        processing_fee:
          Number(Number(financialInfo?.processingFee).toFixed(2)) ?? 100,
        occupancy_tax:
          Number(Number(financialInfo?.occupancyTax?.amount).toFixed(2)) ?? 0,
        security_deposit:
          totalPayments === 1
            ? Number(Number(financialInfo.securityDeposit).toFixed(2))
            : 0,
        other_fee:
          totalPayments === 1
            ? financialInfo?.otherFees.reduce((sum, fee) => sum + fee.amount, 0)
            : 0,
      });
      continue;
    } else if (i === totalPayments - 1) {
      // Calculate the last payment as the remainder to ensure exact total
      const previousPaymentsTotal = newPayments.reduce(
        (sum, payment) => sum + payment.rent,
        0
      );
      const lastPaymentAmount =
        Math.round((rent - previousPaymentsTotal) * 100) / 100;

      newPayments.push({
        due_date: lastPaymentDate,
        rent: lastPaymentAmount,
        security_deposit:
          Number(Number(financialInfo?.securityDeposit).toFixed(2)) ?? 0,
        other_fee: financialInfo?.otherFees.reduce(
          (sum, fee) => sum + fee.amount,
          0
        ),
      });
      continue;
    } else {
      // For intermediate payments, calculate evenly distributed dates
      const firstDate = dayjs(firstPaymentDate);
      const lastDate = dayjs(lastPaymentDate);
      const totalInterval = lastDate.diff(firstDate, 'day');

      // Calculate position as a fraction between 0 and 1
      const fraction = i / (totalPayments - 1);
      const daysFromStart = Math.round(totalInterval * fraction);

      const dueDate = firstDate.add(daysFromStart, 'day').format('YYYY-MM-DD');
      newPayments.push({
        due_date: dueDate,
        rent: paymentAmount,
      });
      continue;
    }
  }

  return newPayments;
};

export const calculateNumberOfDefaultPayments = (dates: {
  from: Date;
  to: Date;
}): number => {
  if (!dates) {
    return 1;
  }

  return dayjs(dates.from).diff(dayjs(), 'day') >= 45 ? 2 : 1;
};

export const calculateGrandTotalForPaymentItem = (payment: Payment) => {
  const rentAmount = payment.rent ?? 0;
  const processingFee = payment.processing_fee ?? 0;
  const otherFee = payment.other_fee ?? 0;
  const securityDeposit = payment.security_deposit ?? 0;
  const occupancyTax = payment.occupancy_tax ?? 0;

  return rentAmount + processingFee + otherFee + securityDeposit + occupancyTax;
};
